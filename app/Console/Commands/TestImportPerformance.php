<?php

namespace App\Console\Commands;

use App\Imports\SimpleHighPerformanceUserImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class TestImportPerformance extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:import-performance 
                            {file? : Excel file to import (relative to storage/app/testing/)}
                            {--user-role=SO : User role for import}
                            {--clear-data : Clear existing data before import}';

    /**
     * The console command description.
     */
    protected $description = 'Test Excel import performance with generated test data';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $file = $this->argument('file') ?? 'so_10k_test.xlsx';
        $userRole = $this->option('user-role');
        $clearData = $this->option('clear-data');
        
        $filePath = storage_path("app/testing/{$file}");
        
        if (!file_exists($filePath)) {
            $this->error("❌ File not found: {$filePath}");
            $this->info("Available files:");
            $files = glob(storage_path('app/testing/*.xlsx'));
            foreach ($files as $availableFile) {
                $this->line("  - " . basename($availableFile));
            }
            return 1;
        }

        $this->info("🚀 Testing Import Performance");
        $this->info("📁 File: {$file}");
        $this->info("👤 User Role: {$userRole}");
        $this->newLine();

        // Display file information
        $this->displayFileInfo($filePath);
        
        // Clear existing data if requested
        if ($clearData) {
            $this->clearExistingData($userRole);
        }

        // Get initial counts
        $initialCount = DB::table('users')->where('user_type', $userRole)->count();
        $this->info("📊 Initial {$userRole} count: {$initialCount}");
        $this->newLine();

        // Perform the import
        $this->info("🔄 Starting import...");
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        try {
            $batchId = 'perf_test_' . time();
            $import = new SimpleHighPerformanceUserImport($userRole, $batchId);
            
            Excel::import($import, $filePath);
            
            $endTime = microtime(true);
            $endMemory = memory_get_peak_usage(true);
            
            // Get final counts
            $finalCount = DB::table('users')->where('user_type', $userRole)->count();
            $importedCount = $finalCount - $initialCount;
            
            // Calculate metrics
            $executionTime = $endTime - $startTime;
            $memoryUsed = ($endMemory - $startMemory) / 1024 / 1024; // MB
            $rowsPerSecond = $importedCount > 0 ? $importedCount / $executionTime : 0;
            
            $this->displayResults($importedCount, $executionTime, $memoryUsed, $rowsPerSecond, $endMemory);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("❌ Import failed: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }

    /**
     * Display file information
     */
    private function displayFileInfo(string $filePath): void
    {
        $fileSize = filesize($filePath);
        $fileSizeMB = round($fileSize / 1024 / 1024, 2);
        
        $this->info("📊 File Information:");
        $this->line("   Size: {$fileSizeMB} MB");
        $this->line("   Path: {$filePath}");
        
        // Estimate row count (rough calculation)
        $estimatedRows = intval($fileSize / 100); // Rough estimate
        $this->line("   Estimated rows: ~{$estimatedRows}");
        $this->newLine();
    }

    /**
     * Clear existing data
     */
    private function clearExistingData(string $userRole): void
    {
        $this->warn("🗑️  Clearing existing {$userRole} data...");
        
        $deletedCount = DB::table('users')->where('user_type', $userRole)->delete();
        $this->info("   Deleted {$deletedCount} existing records");
        $this->newLine();
    }

    /**
     * Display import results
     */
    private function displayResults(int $importedCount, float $executionTime, float $memoryUsed, float $rowsPerSecond, int $peakMemory): void
    {
        $this->newLine();
        $this->info("✅ Import Completed Successfully!");
        $this->newLine();
        
        // Performance metrics table
        $headers = ['Metric', 'Value', 'Performance Rating'];
        $rows = [
            ['Records Imported', number_format($importedCount), $this->getRating('records', $importedCount)],
            ['Execution Time', round($executionTime, 2) . 's', $this->getRating('time', $executionTime)],
            ['Processing Speed', round($rowsPerSecond, 0) . ' rows/sec', $this->getRating('speed', $rowsPerSecond)],
            ['Memory Used', round($memoryUsed, 1) . ' MB', $this->getRating('memory', $memoryUsed)],
            ['Peak Memory', round($peakMemory / 1024 / 1024, 1) . ' MB', $this->getRating('peak_memory', $peakMemory / 1024 / 1024)],
            ['Memory per Row', round($memoryUsed / $importedCount * 1024, 2) . ' KB', $this->getRating('memory_per_row', $memoryUsed / $importedCount * 1024)]
        ];
        
        $this->table($headers, $rows);
        
        // Performance analysis
        $this->newLine();
        $this->info("📈 Performance Analysis:");
        
        if ($rowsPerSecond > 1000) {
            $this->line("   🚀 <fg=green>EXCELLENT</> - Rocket-fast performance!</>");
        } elseif ($rowsPerSecond > 500) {
            $this->line("   ⚡ <fg=yellow>GOOD</> - High performance achieved");
        } elseif ($rowsPerSecond > 100) {
            $this->line("   📊 <fg=blue>AVERAGE</> - Acceptable performance");
        } else {
            $this->line("   🐌 <fg=red>SLOW</> - Performance needs optimization");
        }
        
        // Memory efficiency
        $memoryPerRow = $memoryUsed / $importedCount * 1024; // KB per row
        if ($memoryPerRow < 1) {
            $this->line("   💾 <fg=green>MEMORY EFFICIENT</> - Excellent memory usage");
        } elseif ($memoryPerRow < 5) {
            $this->line("   💾 <fg=yellow>MEMORY GOOD</> - Good memory usage");
        } else {
            $this->line("   💾 <fg=red>MEMORY HIGH</> - Consider optimization");
        }
        
        // Recommendations
        $this->newLine();
        $this->info("💡 Recommendations:");
        
        if ($importedCount > 50000 && $rowsPerSecond < 500) {
            $this->line("   • Consider using streaming import for large files");
        }
        
        if ($memoryPerRow > 2) {
            $this->line("   • Optimize memory usage with smaller batch sizes");
        }
        
        if ($executionTime > 60) {
            $this->line("   • Consider queue-based processing for large imports");
        }
        
        $this->line("   • Monitor database performance during peak usage");
        $this->line("   • Consider adding progress tracking for user experience");
    }

    /**
     * Get performance rating
     */
    private function getRating(string $metric, float $value): string
    {
        switch ($metric) {
            case 'records':
                if ($value > 50000) return '🚀 Massive';
                if ($value > 10000) return '⚡ Large';
                if ($value > 1000) return '📊 Medium';
                return '🔹 Small';
                
            case 'time':
                if ($value < 10) return '🚀 Lightning';
                if ($value < 30) return '⚡ Fast';
                if ($value < 60) return '📊 Good';
                return '🐌 Slow';
                
            case 'speed':
                if ($value > 1000) return '🚀 Rocket';
                if ($value > 500) return '⚡ Fast';
                if ($value > 100) return '📊 Good';
                return '🐌 Slow';
                
            case 'memory':
            case 'peak_memory':
                if ($value < 100) return '💾 Efficient';
                if ($value < 500) return '📊 Good';
                if ($value < 1000) return '⚠️ High';
                return '🔴 Very High';
                
            case 'memory_per_row':
                if ($value < 1) return '💾 Excellent';
                if ($value < 2) return '📊 Good';
                if ($value < 5) return '⚠️ Fair';
                return '🔴 High';
                
            default:
                return '📊 Normal';
        }
    }
}
