<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class RunHighPerformanceWorkers extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:work-high-performance 
                            {--workers=4 : Number of worker processes}
                            {--memory=2048 : Memory limit in MB}
                            {--timeout=3600 : Timeout in seconds}
                            {--sleep=3 : Sleep time when no jobs available}
                            {--tries=3 : Number of attempts}';

    /**
     * The console command description.
     */
    protected $description = 'Run high-performance queue workers optimized for Excel imports';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $workers = (int) $this->option('workers');
        $memory = (int) $this->option('memory');
        $timeout = (int) $this->option('timeout');
        $sleep = (int) $this->option('sleep');
        $tries = (int) $this->option('tries');

        $this->info("🚀 Starting {$workers} high-performance queue workers...");
        
        // Start workers for different queue types
        $this->startWorkers($workers, $memory, $timeout, $sleep, $tries);
        
        return 0;
    }

    /**
     * Start optimized workers for different queue types
     */
    private function startWorkers(int $workers, int $memory, int $timeout, int $sleep, int $tries): void
    {
        $queues = [
            'streaming_imports' => [
                'workers' => max(1, intval($workers * 0.3)), // 30% for streaming
                'memory' => $memory * 2, // Double memory for streaming
                'timeout' => $timeout * 2, // Double timeout for streaming
            ],
            'high_performance_imports' => [
                'workers' => max(1, intval($workers * 0.5)), // 50% for high performance
                'memory' => $memory,
                'timeout' => $timeout,
            ],
            'default' => [
                'workers' => max(1, intval($workers * 0.2)), // 20% for default
                'memory' => intval($memory * 0.5), // Half memory for default
                'timeout' => intval($timeout * 0.5), // Half timeout for default
            ],
        ];

        foreach ($queues as $queueName => $config) {
            $this->startQueueWorkers($queueName, $config, $sleep, $tries);
        }

        $this->info("✅ All high-performance workers started successfully!");
        $this->displayWorkerStatus($queues);
    }

    /**
     * Start workers for a specific queue
     */
    private function startQueueWorkers(string $queueName, array $config, int $sleep, int $tries): void
    {
        for ($i = 1; $i <= $config['workers']; $i++) {
            $command = [
                'queue:work',
                '--queue' => $queueName,
                '--memory' => $config['memory'],
                '--timeout' => $config['timeout'],
                '--sleep' => $sleep,
                '--tries' => $tries,
                '--daemon' => true,
                '--verbose' => true,
            ];

            // Start worker in background
            $this->startBackgroundWorker($command, $queueName, $i);
        }

        $this->line("  ✓ Started {$config['workers']} workers for queue: {$queueName}");
    }

    /**
     * Start a background worker process
     */
    private function startBackgroundWorker(array $command, string $queueName, int $workerNumber): void
    {
        $logFile = storage_path("logs/queue-worker-{$queueName}-{$workerNumber}.log");
        
        // Build the artisan command
        $artisanCommand = 'php ' . base_path('artisan') . ' ' . implode(' ', array_map(function($key, $value) {
            if (is_numeric($key)) {
                return $value;
            }
            return "--{$key}={$value}";
        }, array_keys($command), $command));

        // Start process in background with logging
        $fullCommand = "nohup {$artisanCommand} >> {$logFile} 2>&1 &";
        
        exec($fullCommand);
        
        Log::info("Started queue worker", [
            'queue' => $queueName,
            'worker_number' => $workerNumber,
            'command' => $artisanCommand,
            'log_file' => $logFile
        ]);
    }

    /**
     * Display worker status information
     */
    private function displayWorkerStatus(array $queues): void
    {
        $this->newLine();
        $this->info("📊 Worker Configuration:");
        
        $headers = ['Queue', 'Workers', 'Memory (MB)', 'Timeout (s)', 'Purpose'];
        $rows = [];
        
        foreach ($queues as $queueName => $config) {
            $purpose = match($queueName) {
                'streaming_imports' => 'Large files (>50MB)',
                'high_performance_imports' => 'Standard files (10-50MB)',
                'default' => 'Small files (<10MB)',
                default => 'General purpose'
            };
            
            $rows[] = [
                $queueName,
                $config['workers'],
                $config['memory'],
                $config['timeout'],
                $purpose
            ];
        }
        
        $this->table($headers, $rows);
        
        $this->newLine();
        $this->info("📝 Monitoring Commands:");
        $this->line("  • Monitor queues: php artisan queue:monitor");
        $this->line("  • Check failed jobs: php artisan queue:failed");
        $this->line("  • View logs: tail -f storage/logs/queue-worker-*.log");
        
        $this->newLine();
        $this->info("🔧 Performance Tips:");
        $this->line("  • Use Redis for better queue performance");
        $this->line("  • Monitor memory usage and adjust limits as needed");
        $this->line("  • Scale workers based on import volume");
        $this->line("  • Use supervisor for production worker management");
    }

    /**
     * Get supervisor configuration for production
     */
    public function getSupervisorConfig(): string
    {
        return <<<EOT
[program:laravel-worker-streaming]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/artisan queue:work redis --queue=streaming_imports --sleep=3 --tries=3 --memory=4096 --timeout=7200
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/storage/logs/worker-streaming.log

[program:laravel-worker-high-performance]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/artisan queue:work redis --queue=high_performance_imports --sleep=3 --tries=3 --memory=2048 --timeout=3600
autostart=true
autorestart=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/path/to/storage/logs/worker-high-performance.log

[program:laravel-worker-default]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/artisan queue:work redis --queue=default --sleep=3 --tries=3 --memory=1024 --timeout=1800
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/storage/logs/worker-default.log
EOT;
    }
}
