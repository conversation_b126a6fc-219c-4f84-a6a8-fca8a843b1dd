<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class GenerateSOTestData extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'generate:so-test-data 
                            {--count=100000 : Number of SO records to generate}
                            {--file=so_test_data.xlsx : Output file name}
                            {--batch=10000 : Batch size for memory optimization}';

    /**
     * The console command description.
     */
    protected $description = 'Generate dummy SO (Sales Officer) test data for performance testing';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $count = (int) $this->option('count');
        $fileName = $this->option('file');
        $batchSize = (int) $this->option('batch');
        
        $this->info("🚀 Generating {$count} SO records for performance testing...");
        $this->info("📁 Output file: {$fileName}");
        $this->info("📦 Batch size: {$batchSize}");
        $this->newLine();

        $startTime = microtime(true);
        
        try {
            $this->generateSOData($count, $fileName, $batchSize);
            
            $executionTime = microtime(true) - $startTime;
            $this->info("✅ Successfully generated {$count} SO records!");
            $this->info("⏱️  Execution time: " . round($executionTime, 2) . " seconds");
            $this->info("📊 Generation rate: " . round($count / $executionTime, 0) . " records/second");
            $this->info("📁 File saved to: storage/app/testing/{$fileName}");
            
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Error generating test data: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Generate SO test data with realistic variations
     */
    private function generateSOData(int $count, string $fileName, int $batchSize): void
    {
        // Create directory if it doesn't exist
        $directory = storage_path('app/testing');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        $filePath = $directory . '/' . $fileName;
        
        // Create new spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Set headers for SO import
        $headers = [
            'so_user_code',
            'so_name', 
            'so_teritory_code',
            'rsm_code',
            'asm_code',
            'so_contact_number',
            'region_name',
            'city',
            'state',
            'pincode',
            'cfa_code',
            'email_id'
        ];
        
        // Write headers
        foreach ($headers as $index => $header) {
            $sheet->setCellValueByColumnAndRow($index + 1, 1, $header);
        }
        
        // Style headers
        $headerRange = 'A1:' . chr(65 + count($headers) - 1) . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('4CAF50');
        
        // Generate data in batches for memory efficiency
        $currentRow = 2;
        $processedCount = 0;
        
        $progressBar = $this->output->createProgressBar($count);
        $progressBar->start();
        
        while ($processedCount < $count) {
            $batchCount = min($batchSize, $count - $processedCount);
            
            for ($i = 0; $i < $batchCount; $i++) {
                $recordNumber = $processedCount + $i + 1;
                $soData = $this->generateSORecord($recordNumber);
                
                // Write row data
                foreach ($soData as $colIndex => $value) {
                    $sheet->setCellValueByColumnAndRow($colIndex + 1, $currentRow, $value);
                }
                
                $currentRow++;
                $progressBar->advance();
            }
            
            $processedCount += $batchCount;
            
            // Force garbage collection every batch
            if ($processedCount % ($batchSize * 5) === 0) {
                gc_collect_cycles();
                $this->info("\n🔄 Processed {$processedCount}/{$count} records...");
            }
        }
        
        $progressBar->finish();
        $this->newLine();
        
        // Auto-size columns for better readability
        foreach (range('A', chr(65 + count($headers) - 1)) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }
        
        // Save the file
        $this->info("💾 Saving file...");
        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);
        
        // Clear memory
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        gc_collect_cycles();
    }

    /**
     * Generate a single SO record with realistic data
     */
    private function generateSORecord(int $recordNumber): array
    {
        // Predefined data arrays for realistic variations
        $regions = ['North', 'South', 'East', 'West', 'Central', 'Northeast'];
        $states = [
            'Maharashtra', 'Karnataka', 'Tamil Nadu', 'Gujarat', 'Rajasthan',
            'Uttar Pradesh', 'West Bengal', 'Madhya Pradesh', 'Bihar', 'Odisha',
            'Telangana', 'Andhra Pradesh', 'Kerala', 'Punjab', 'Haryana'
        ];
        $cities = [
            'Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata', 'Hyderabad',
            'Pune', 'Ahmedabad', 'Surat', 'Jaipur', 'Lucknow', 'Kanpur',
            'Nagpur', 'Indore', 'Thane', 'Bhopal', 'Visakhapatnam', 'Pimpri'
        ];
        
        // Generate unique codes with proper formatting
        $soCode = 'SO' . str_pad($recordNumber, 6, '0', STR_PAD_LEFT);
        $rsmCode = 'RSM' . str_pad(($recordNumber % 100) + 1, 3, '0', STR_PAD_LEFT);
        $asmCode = 'ASM' . str_pad(($recordNumber % 500) + 1, 4, '0', STR_PAD_LEFT);
        $territoryCode = 'TER' . str_pad(($recordNumber % 1000) + 1, 4, '0', STR_PAD_LEFT);
        $cfaCode = 'CFA' . str_pad(($recordNumber % 50) + 1, 3, '0', STR_PAD_LEFT);
        
        // Generate realistic names
        $firstNames = [
            'Rajesh', 'Priya', 'Amit', 'Sunita', 'Vikash', 'Kavita', 'Suresh', 'Meera',
            'Ravi', 'Pooja', 'Anil', 'Deepika', 'Manoj', 'Sita', 'Ramesh', 'Geeta',
            'Ajay', 'Nisha', 'Vinod', 'Rekha', 'Santosh', 'Usha', 'Prakash', 'Lata'
        ];
        $lastNames = [
            'Sharma', 'Patel', 'Singh', 'Kumar', 'Gupta', 'Jain', 'Agarwal', 'Verma',
            'Yadav', 'Mishra', 'Tiwari', 'Pandey', 'Shah', 'Mehta', 'Joshi', 'Reddy',
            'Nair', 'Pillai', 'Rao', 'Iyer', 'Menon', 'Bhat', 'Shetty', 'Kulkarni'
        ];
        
        $firstName = $firstNames[$recordNumber % count($firstNames)];
        $lastName = $lastNames[($recordNumber * 7) % count($lastNames)];
        $fullName = $firstName . ' ' . $lastName;
        
        // Generate contact details
        $mobileNumber = '9' . str_pad(mt_rand(*********, *********), 9, '0', STR_PAD_LEFT);
        $email = strtolower($firstName . '.' . $lastName . $recordNumber) . '@company.com';
        
        // Generate location details
        $region = $regions[$recordNumber % count($regions)];
        $state = $states[$recordNumber % count($states)];
        $city = $cities[$recordNumber % count($cities)];
        $pincode = str_pad(mt_rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        
        return [
            $soCode,                    // so_user_code
            $fullName,                  // so_name
            $territoryCode,             // so_teritory_code
            $rsmCode,                   // rsm_code
            $asmCode,                   // asm_code
            $mobileNumber,              // so_contact_number
            $region,                    // region_name
            $city,                      // city
            $state,                     // state
            $pincode,                   // pincode
            $cfaCode,                   // cfa_code
            $email                      // email_id
        ];
    }

    /**
     * Display file information
     */
    private function displayFileInfo(string $filePath): void
    {
        if (file_exists($filePath)) {
            $fileSize = filesize($filePath);
            $fileSizeMB = round($fileSize / 1024 / 1024, 2);
            
            $this->info("📊 File Information:");
            $this->line("   Size: {$fileSizeMB} MB");
            $this->line("   Path: {$filePath}");
            
            if ($fileSizeMB > 50) {
                $this->warn("⚠️  Large file detected! This will test streaming import capabilities.");
            }
        }
    }
}
