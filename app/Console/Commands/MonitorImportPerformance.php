<?php

namespace App\Console\Commands;

use App\Services\ImportPerformanceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class MonitorImportPerformance extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'import:monitor 
                            {--watch : Watch mode for real-time monitoring}
                            {--interval=5 : Refresh interval in seconds for watch mode}
                            {--batch= : Monitor specific batch ID}
                            {--days=7 : Number of days for analytics}';

    /**
     * The console command description.
     */
    protected $description = 'Monitor Excel import performance and display real-time metrics';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Excel Import Performance Monitor');
        $this->newLine();

        if ($this->option('batch')) {
            return $this->monitorSpecificBatch();
        }

        if ($this->option('watch')) {
            return $this->watchMode();
        }

        return $this->showSummary();
    }

    /**
     * Monitor a specific batch
     */
    private function monitorSpecificBatch(): int
    {
        $batchId = $this->option('batch');
        $this->info("Monitoring batch: {$batchId}");
        $this->newLine();

        $progress = ImportPerformanceService::getImportProgress($batchId);
        
        if (!$progress) {
            $this->error("Batch {$batchId} not found or not active");
            return 1;
        }

        $this->displayBatchProgress($batchId, $progress);
        return 0;
    }

    /**
     * Watch mode for real-time monitoring
     */
    private function watchMode(): int
    {
        $interval = (int) $this->option('interval');
        
        $this->info("Starting watch mode (refresh every {$interval}s)");
        $this->info("Press Ctrl+C to exit");
        $this->newLine();

        while (true) {
            // Clear screen
            system('clear');
            
            $this->info('🚀 Excel Import Performance Monitor - ' . now()->format('Y-m-d H:i:s'));
            $this->newLine();

            $this->displayActiveImports();
            $this->newLine();
            $this->displayQueueStatus();
            $this->newLine();
            $this->displaySystemMetrics();

            sleep($interval);
        }

        return 0;
    }

    /**
     * Show performance summary
     */
    private function showSummary(): int
    {
        $days = (int) $this->option('days');
        
        $this->info("Performance Summary (Last {$days} days)");
        $this->newLine();

        $analytics = ImportPerformanceService::getPerformanceAnalytics($days);
        
        if (empty($analytics['analytics'])) {
            $this->warn('No import data found for the specified period');
            return 0;
        }

        $this->displayAnalytics($analytics);
        $this->newLine();
        $this->displayTopPerformers();
        $this->newLine();
        $this->displayRecommendations($analytics);

        return 0;
    }

    /**
     * Display active imports
     */
    private function displayActiveImports(): void
    {
        $activeImports = DB::table('import_performance_metrics')
            ->whereNull('completed_at')
            ->where('started_at', '>=', now()->subHours(6))
            ->get();

        $this->info('📊 Active Imports');
        
        if ($activeImports->isEmpty()) {
            $this->line('  No active imports');
            return;
        }

        $headers = ['Batch ID', 'Type', 'Role', 'Processed', 'Speed (rows/s)', 'Memory (MB)', 'Started'];
        $rows = [];

        foreach ($activeImports as $import) {
            $rows[] = [
                substr($import->batch_id, 0, 12) . '...',
                $import->import_type,
                $import->user_role,
                number_format($import->processed_rows),
                number_format($import->rows_per_second, 1),
                number_format($import->memory_peak_mb),
                $import->started_at
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Display queue status
     */
    private function displayQueueStatus(): void
    {
        $this->info('🔄 Queue Status');
        
        $queues = ['default', 'high_performance_imports', 'streaming_imports'];
        $headers = ['Queue', 'Pending', 'Failed', 'Status'];
        $rows = [];

        foreach ($queues as $queue) {
            $pending = DB::table('jobs')->where('queue', $queue)->count();
            $failed = DB::table('failed_jobs')->where('queue', $queue)->count();
            
            $status = $pending > 100 ? '🔴 High Load' : ($pending > 50 ? '🟡 Moderate' : '🟢 Normal');
            
            $rows[] = [$queue, $pending, $failed, $status];
        }

        $this->table($headers, $rows);
    }

    /**
     * Display system metrics
     */
    private function displaySystemMetrics(): void
    {
        $this->info('💻 System Metrics');
        
        $memoryUsage = memory_get_usage(true) / 1024 / 1024;
        $memoryPeak = memory_get_peak_usage(true) / 1024 / 1024;
        $memoryLimit = ini_get('memory_limit');
        
        $this->line("  Memory Usage: " . number_format($memoryUsage, 1) . "MB / " . number_format($memoryPeak, 1) . "MB peak (Limit: {$memoryLimit})");
        $this->line("  PHP Version: " . PHP_VERSION);
        $this->line("  Max Execution Time: " . ini_get('max_execution_time') . "s");
        
        try {
            $dbConnections = DB::table('information_schema.processlist')->count();
            $this->line("  Database Connections: {$dbConnections}");
        } catch (\Exception $e) {
            $this->line("  Database Connections: Unable to fetch");
        }
    }

    /**
     * Display batch progress
     */
    private function displayBatchProgress(string $batchId, array $progress): void
    {
        $this->info("Batch Progress: {$batchId}");
        $this->newLine();

        $this->line("Processed Rows: " . number_format($progress['processed_rows']));
        $this->line("Successful Rows: " . number_format($progress['successful_rows']));
        $this->line("Failed Rows: " . number_format($progress['failed_rows']));
        $this->line("Processing Speed: " . number_format($progress['rows_per_second'], 2) . " rows/second");
        $this->line("Memory Usage: " . number_format($progress['memory_usage']) . " MB");
        $this->line("Execution Time: " . number_format($progress['execution_time'], 2) . " seconds");
        $this->line("Last Update: " . $progress['last_update']);
    }

    /**
     * Display analytics
     */
    private function displayAnalytics(array $analytics): void
    {
        $this->info('📈 Performance Analytics');
        
        $headers = ['Type', 'Role', 'Imports', 'Avg Speed', 'Max Speed', 'Avg Memory', 'Success Rate'];
        $rows = [];

        foreach ($analytics['analytics'] as $metric) {
            $rows[] = [
                $metric->import_type,
                $metric->user_role,
                $metric->total_imports,
                number_format($metric->avg_rows_per_second, 1),
                number_format($metric->max_rows_per_second, 1),
                number_format($metric->avg_memory_usage, 1) . 'MB',
                number_format($metric->avg_success_rate, 1) . '%'
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Display top performers
     */
    private function displayTopPerformers(): void
    {
        $topPerformers = ImportPerformanceService::getTopPerformers(5);
        
        if (empty($topPerformers)) {
            return;
        }

        $this->info('🏆 Top Performers (Last 30 days)');
        
        $headers = ['Batch ID', 'Type', 'Role', 'Rows', 'Speed', 'Memory', 'Date'];
        $rows = [];

        foreach ($topPerformers as $performer) {
            $rows[] = [
                substr($performer->batch_id, 0, 12) . '...',
                $performer->import_type,
                $performer->user_role,
                number_format($performer->total_rows),
                number_format($performer->rows_per_second, 1),
                number_format($performer->memory_peak_mb) . 'MB',
                $performer->completed_at
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Display recommendations
     */
    private function displayRecommendations(array $analytics): void
    {
        if (empty($analytics['recommendations'])) {
            return;
        }

        $this->info('💡 Optimization Recommendations');
        
        foreach ($analytics['recommendations'] as $recommendation) {
            $priority = match($recommendation['priority']) {
                'high' => '🔴',
                'medium' => '🟡',
                'low' => '🟢',
                default => '⚪'
            };
            
            $this->line("  {$priority} {$recommendation['message']}");
        }
    }
}
