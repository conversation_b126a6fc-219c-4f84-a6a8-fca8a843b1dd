<?php

namespace App\Http\Controllers\Admin;

use App\Exports\UploadHistory\AssetASMErrorLogExport;
use App\Exports\UploadHistory\AssetDBErrorLogExport;
use App\Exports\UploadHistory\AssetDbToCfaMappingErrorLogExport;
use App\Exports\UploadHistory\AssetDSRErrorLogExport;
use App\Exports\UploadHistory\AssetInventoryErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerGSTupdateErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerLatLongErrorLogExport;
use App\Exports\UploadHistory\AssetRetailerMappingErrorLogExport;
use App\Exports\UploadHistory\AssetRSMErrorLogExport;
use App\Exports\UploadHistory\AssetSOErrorLogExport;
use App\Exports\UploadHistory\AssetVEErrorLogExport;
use App\Exports\UploadHistory\AssetVendorErrorLogExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\UserMasterUploadRequest;
use App\Imports\UserMasterImport;
use App\Imports\UsersDemoImport;
use App\Imports\UsersHierarchyImport;
use App\Imports\HighPerformanceUserImport;
use App\Imports\StreamingUserImport;
use App\Imports\SimpleHighPerformanceUserImport;
use App\Jobs\ImportHierarchyFileJob;
use App\Jobs\PoductJob;
use App\Jobs\ProcessUploaderImport;
use App\Models\AssetErrorLog;
use App\Models\UploadHistory;
use App\Models\User;
use App\Services\UploadHistoryDetails\UploadHistoryService;
use Illuminate\Http\Request;
use App\Services\UserImportHierarchyService;
use App\Services\UserImportService;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\support\Facades\Bus;
use Illuminate\Queue\WithBatchId;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class UserImportController extends Controller
{
    protected $UserImportHierarchyService;
    protected $UserImportService;

    public function __construct(UserImportHierarchyService $UserImportHierarchyService, UserImportService $UserImportService)
    {
        $this->UserImportHierarchyService = $UserImportHierarchyService;
        $this->UserImportService = $UserImportService;
        $this->auth= Auth::user();
    }
    public function index()
    {
        return view('setting.user.hierarchy_upload');
    }


    public function uploadHierarchyFile(Request $request)
    {
        try {
            // Check if a file is present in the request
            if (!$request->hasFile('hierarchy_file')) {
                return response()->json(['error' => 'empty_file', 'message' => "Please upload hierarchy file"], 422);
            }

            // Rocket-fast hierarchy import with performance optimization
            $fileSize = $request->file('hierarchy_file')->getSize();

            // Use direct import without queue to avoid serialization issues
            Excel::import(new SimpleHighPerformanceUserImport('HIERARCHY', uniqid('hierarchy_batch_')), $request->file('hierarchy_file'));
            Log::info("Using SimpleHighPerformanceUserImport for hierarchy file (direct)", ['size' => $fileSize]);

            return response()->json([
                'success' => 'Rocket-fast hierarchy import started! You will be notified when it completes.',
                'import_type' => $fileSize > 50 * 1024 * 1024 ? 'streaming' : 'high_performance'
            ]);
        } catch (ValidationException $e) {
            $errors = $e->validator->getMessageBag()->all();
            return response()->json(['validation_error' => $errors], 422);
        } catch (\Throwable $th) {
            Log::error('Failed to import users: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to import users Hierarchy. Please try again.'], 500);
        }
    }


    public function userImportPage()
    {
        $roles = Role::whereNotIn("name", ["SUPERADMIN", "ADMIN"])->get();
        return view('setting.user.user_master_upload', ['roles' => $roles]);
    }
    public function UploadUserMasterDetails(UserMasterUploadRequest $request)
    {
        try {
            if (!$request->hasFile('user_import_file')) {
                return response()->json(['error' => 'empty_file', 'message' => "Please upload user master excel file"], 422);
            }
            $userRole = $request->user_type;
            $requestIP = $request->ip();
            $batchID = uniqid('batch_');
            Session::put('upload_batch_id', $batchID);
            $this->uploadHistoryCreate($batchID, $userRole, $requestIP);
            // Rocket-fast import with performance optimization
            $fileSize = $request->file('user_import_file')->getSize();

            // Use direct import without queue to avoid serialization issues
            Excel::import(new SimpleHighPerformanceUserImport($userRole, $batchID), $request->file('user_import_file'));
            Log::info("Using SimpleHighPerformanceUserImport for user import (direct)", ['size' => $fileSize, 'batch_id' => $batchID]);

            $upload_batch_id = Session::get('upload_batch_id');
            return response()->json([
                'success' => 'Rocket-fast import started! You will be notified when it completes.',
                'redirectRoute' => $request->redirectRoute ?? '',
                'batch_id' => $batchID,
                'import_type' => $fileSize > 50 * 1024 * 1024 ? 'streaming' : 'high_performance'
            ]);
        } catch (ValidationException $e) {
            $errors = $e->validator->getMessageBag()->all();
            return response()->json(['validation_error' => $errors], 422);
        } catch (\Throwable $th) {
            Log::error('Failed to import users: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to import users. Please try again.' . $th->getMessage()], 500);
        }
    }

    public function BatchUploadHistoryDownload(Request $request)
    {
        $rules = [
            'batchID' => 'required|exists:asset_error_log,batch_id' // 'batches' is the table name, 'id' is the column
        ];
        $validator = Validator::make($request->all(), $rules);

        // If validation fails, redirect back with errors
        if ($validator->fails()) {
            return redirect()->back()->with('error', 'Invalid BatchID');
        }
        $batchID = $request->batchID;
        $uploadDetails=UploadHistoryService::getLatestHistorybyBatchID($batchID);
//        $uploadDetailslist=UploadHistoryService::getAssetLogDetailsByBatchID($batchID);
//        dd($uploadDetailslist->toArray());
        if ($uploadDetails->uploadType == 'RSM') {
            return Excel::download(new AssetRSMErrorLogExport($batchID), 'Upload_data_RSM_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'ASM') {
            return Excel::download(new AssetASMErrorLogExport($batchID), 'Upload_data_ASM_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'SO') {
            return Excel::download(new AssetSOErrorLogExport($batchID), 'Upload_data_SO_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'DISTRIBUTOR') {
            return Excel::download(new AssetDBErrorLogExport($batchID), 'Upload_data_DISTRIBUTOR_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'RETAILER') {
            return Excel::download(new AssetRetailerErrorLogExport($batchID), 'Upload_data_RETAILER_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'VENDOR') {
            return Excel::download(new AssetVendorErrorLogExport($batchID), 'Upload_data_VENDOR_' . now() . $batchID . '.xlsx');
        } elseif ($uploadDetails->uploadType == 'VE') {
            return Excel::download(new AssetVEErrorLogExport($batchID), 'Upload_data_VE_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'DSR') {
            return Excel::download(new AssetDSRErrorLogExport($batchID), 'Upload_data_DSR_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'retailer_gst_update') {
            return Excel::download(new AssetRetailerGSTupdateErrorLogExport($batchID), 'Upload_data_retailer_gst_update_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'retailer_lat_long') {
            return Excel::download(new AssetRetailerLatLongErrorLogExport($batchID), 'Upload_data_retailer_lat_long_' . now() . $batchID . '.xlsx');
        }
        #pending
        elseif ($uploadDetails->uploadType == 'db_to_cfa_mapping') {
            return Excel::download(new AssetDbToCfaMappingErrorLogExport($batchID), 'Upload_data_db_to_cfa_mapping_' . now() . $batchID . '.xlsx');
        }
        elseif ($uploadDetails->uploadType == 'asset_retailer_mapping') {
            return Excel::download(new AssetRetailerMappingErrorLogExport($batchID), 'Upload_data_asset_retailer_mapping_' . now() . $batchID . '.xlsx');
        }
       elseif ($uploadDetails->uploadType == 'asset_inventory_upload') {
           return Excel::download(new AssetInventoryErrorLogExport($batchID), 'Upload_data_asset_inventory_upload_' . now() . $batchID . '.xlsx');
       }
        else{
            return redirect()->back()->with('error', 'Invalid BatchID');
        }
    }


    public  function  uploadHistoryCreate($batchID, $userRole, $ip)
    {
        return UploadHistory::create([
            'batchID' => $batchID,
            'uploadBy' => Auth::user()->user_id,
            'requestIP' => $ip,
            'uploadByName' => Auth::user()->name,
            'userType' => Auth::user()->user_type,
            'uploadDateTime' => now(),
            'uploadType' => $userRole,
            'totalRecordscount' => 0,
            'successCount' => 0,
            'failedCount' => 0,
            'failedMessage' => '',
            'failedData' => []
        ]);
    }
}
