<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ImportPerformanceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ImportMonitoringController extends Controller
{
    /**
     * Display the import monitoring dashboard
     */
    public function dashboard()
    {
        $analytics = ImportPerformanceService::getPerformanceAnalytics(30);
        $topPerformers = ImportPerformanceService::getTopPerformers(10);
        
        return view('admin.import-monitoring.dashboard', [
            'analytics' => $analytics,
            'topPerformers' => $topPerformers
        ]);
    }

    /**
     * Get real-time import progress
     */
    public function getProgress(Request $request): JsonResponse
    {
        $batchId = $request->get('batch_id');
        
        if (!$batchId) {
            return response()->json(['error' => 'Batch ID required'], 400);
        }

        $progress = ImportPerformanceService::getImportProgress($batchId);
        
        if (!$progress) {
            return response()->json(['error' => 'Import not found'], 404);
        }

        // Add estimated completion time
        if ($progress['rows_per_second'] > 0 && isset($progress['total_rows'])) {
            $remainingRows = max(0, $progress['total_rows'] - $progress['processed_rows']);
            $estimatedSeconds = $remainingRows / $progress['rows_per_second'];
            $progress['estimated_completion'] = now()->addSeconds($estimatedSeconds)->toISOString();
        }

        return response()->json([
            'success' => true,
            'data' => $progress
        ]);
    }

    /**
     * Get live performance metrics
     */
    public function getLiveMetrics(): JsonResponse
    {
        try {
            // Get active imports
            $activeImports = DB::table('import_performance_metrics')
                ->whereNull('completed_at')
                ->where('started_at', '>=', now()->subHours(6))
                ->select([
                    'batch_id',
                    'import_type',
                    'user_role',
                    'processed_rows',
                    'rows_per_second',
                    'memory_peak_mb',
                    'started_at'
                ])
                ->get();

            // Get queue statistics
            $queueStats = $this->getQueueStatistics();

            // Get system performance
            $systemMetrics = $this->getSystemMetrics();

            // Get recent completions
            $recentCompletions = DB::table('import_performance_metrics')
                ->whereNotNull('completed_at')
                ->where('completed_at', '>=', now()->subHour())
                ->orderBy('completed_at', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'active_imports' => $activeImports,
                    'queue_stats' => $queueStats,
                    'system_metrics' => $systemMetrics,
                    'recent_completions' => $recentCompletions,
                    'timestamp' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get live metrics', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch metrics'
            ], 500);
        }
    }

    /**
     * Get queue statistics
     */
    private function getQueueStatistics(): array
    {
        try {
            $stats = [];
            $queues = ['default', 'high_performance_imports', 'streaming_imports'];

            foreach ($queues as $queue) {
                $pending = DB::table('jobs')->where('queue', $queue)->count();
                $failed = DB::table('failed_jobs')->where('queue', $queue)->count();
                
                $stats[$queue] = [
                    'pending' => $pending,
                    'failed' => $failed,
                    'status' => $pending > 100 ? 'high_load' : ($pending > 50 ? 'moderate' : 'normal')
                ];
            }

            return $stats;
        } catch (\Exception $e) {
            Log::error('Failed to get queue statistics', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get system performance metrics
     */
    private function getSystemMetrics(): array
    {
        try {
            return [
                'memory_usage' => [
                    'current' => memory_get_usage(true) / 1024 / 1024, // MB
                    'peak' => memory_get_peak_usage(true) / 1024 / 1024, // MB
                    'limit' => ini_get('memory_limit')
                ],
                'php_info' => [
                    'version' => PHP_VERSION,
                    'max_execution_time' => ini_get('max_execution_time'),
                    'upload_max_filesize' => ini_get('upload_max_filesize'),
                    'post_max_size' => ini_get('post_max_size')
                ],
                'database' => [
                    'connections' => DB::table('information_schema.processlist')->count(),
                    'version' => DB::select('SELECT VERSION() as version')[0]->version ?? 'unknown'
                ]
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get system metrics', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get performance analytics for a specific period
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $userRole = $request->get('user_role');
        $importType = $request->get('import_type');

        try {
            $query = DB::table('import_performance_metrics')
                ->where('created_at', '>=', now()->subDays($days));

            if ($userRole) {
                $query->where('user_role', $userRole);
            }

            if ($importType) {
                $query->where('import_type', $importType);
            }

            $analytics = $query->selectRaw('
                DATE(created_at) as date,
                COUNT(*) as total_imports,
                AVG(rows_per_second) as avg_speed,
                MAX(rows_per_second) as max_speed,
                AVG(memory_peak_mb) as avg_memory,
                SUM(successful_rows) as total_successful,
                SUM(failed_rows) as total_failed,
                AVG(execution_time) as avg_execution_time
            ')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get analytics', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch analytics'
            ], 500);
        }
    }

    /**
     * Get import history with pagination
     */
    public function getHistory(Request $request): JsonResponse
    {
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);
        $userRole = $request->get('user_role');
        $importType = $request->get('import_type');

        try {
            $query = DB::table('import_performance_metrics')
                ->orderBy('created_at', 'desc');

            if ($userRole) {
                $query->where('user_role', $userRole);
            }

            if ($importType) {
                $query->where('import_type', $importType);
            }

            $total = $query->count();
            $imports = $query->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'imports' => $imports,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => ceil($total / $perPage)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get import history', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch history'
            ], 500);
        }
    }

    /**
     * Get optimization recommendations
     */
    public function getRecommendations(): JsonResponse
    {
        try {
            $analytics = ImportPerformanceService::getPerformanceAnalytics(30);
            $recommendations = $analytics['recommendations'] ?? [];

            // Add system-level recommendations
            $systemRecommendations = $this->getSystemRecommendations();
            $allRecommendations = array_merge($recommendations, $systemRecommendations);

            return response()->json([
                'success' => true,
                'data' => $allRecommendations
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get recommendations', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch recommendations'
            ], 500);
        }
    }

    /**
     * Get system-level optimization recommendations
     */
    private function getSystemRecommendations(): array
    {
        $recommendations = [];

        // Check memory limit
        $memoryLimit = ini_get('memory_limit');
        if (str_contains($memoryLimit, 'M') && (int)$memoryLimit < 2048) {
            $recommendations[] = [
                'type' => 'system',
                'priority' => 'high',
                'message' => "Consider increasing PHP memory limit to 2048M for better performance. Current: {$memoryLimit}",
                'action' => 'Increase memory_limit in php.ini'
            ];
        }

        // Check execution time
        $maxExecutionTime = ini_get('max_execution_time');
        if ($maxExecutionTime > 0 && $maxExecutionTime < 3600) {
            $recommendations[] = [
                'type' => 'system',
                'priority' => 'medium',
                'message' => "Consider increasing max_execution_time for large imports. Current: {$maxExecutionTime}s",
                'action' => 'Set max_execution_time = 0 or increase to 3600'
            ];
        }

        // Check queue configuration
        $queueConnection = config('queue.default');
        if ($queueConnection === 'database') {
            $recommendations[] = [
                'type' => 'queue',
                'priority' => 'high',
                'message' => 'Consider using Redis for queue processing for better performance',
                'action' => 'Configure Redis and set QUEUE_CONNECTION=redis'
            ];
        }

        return $recommendations;
    }

    /**
     * Export performance report
     */
    public function exportReport(Request $request)
    {
        $days = $request->get('days', 30);
        
        try {
            $analytics = ImportPerformanceService::getPerformanceAnalytics($days);
            $topPerformers = ImportPerformanceService::getTopPerformers(20);
            
            $report = [
                'generated_at' => now()->toISOString(),
                'period_days' => $days,
                'analytics' => $analytics,
                'top_performers' => $topPerformers,
                'system_info' => $this->getSystemMetrics()
            ];

            return response()->json($report)
                ->header('Content-Disposition', 'attachment; filename="import-performance-report-' . now()->format('Y-m-d') . '.json"');

        } catch (\Exception $e) {
            Log::error('Failed to export report', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate report'
            ], 500);
        }
    }
}
