<?php

namespace App\Jobs;

use App\Imports\HighPerformanceUserImport;
use App\Imports\StreamingUserImport;
use App\Services\ImportPerformanceService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class HighPerformanceImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 14400; // 4 hours for massive files
    public $tries = 3;
    public $maxExceptions = 1;
    public $backoff = [60, 300, 900]; // Progressive backoff

    protected $filePath;
    protected $userRole;
    protected $batchId;
    protected $importType;
    protected $fileSizeBytes;
    protected $userId;
    protected $userName;

    /**
     * Create a new job instance for rocket-fast imports
     */
    public function __construct(
        string $filePath,
        string $userRole,
        string $batchId,
        string $importType = 'auto',
        int $fileSizeBytes = 0,
        ?string $userId = null,
        ?string $userName = null
    ) {
        $this->filePath = $filePath;
        $this->userRole = $userRole;
        $this->batchId = $batchId;
        $this->importType = $importType;
        $this->fileSizeBytes = $fileSizeBytes;
        $this->userId = $userId;
        $this->userName = $userName;

        // Set queue based on import type for optimal performance
        $this->onQueue($this->getOptimalQueue());
    }

    /**
     * Execute the rocket-fast import job
     */
    public function handle(): void
    {
        $startTime = microtime(true);
        
        try {
            // Initialize performance tracking
            $this->initializePerformanceTracking();
            
            // Optimize environment for import
            $this->optimizeEnvironment();
            
            // Determine optimal import strategy
            $importClass = $this->getOptimalImportClass();
            
            Log::info("HighPerformanceImportJob: Starting import", [
                'batch_id' => $this->batchId,
                'import_class' => get_class($importClass),
                'file_size' => $this->fileSizeBytes,
                'user_role' => $this->userRole
            ]);

            // Execute the import with performance monitoring
            $this->executeImport($importClass);
            
            // Track completion
            $executionTime = microtime(true) - $startTime;
            $this->trackCompletion($executionTime, true);
            
            Log::info("HighPerformanceImportJob: Import completed successfully", [
                'batch_id' => $this->batchId,
                'execution_time' => round($executionTime, 2) . 's'
            ]);

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;
            $this->trackCompletion($executionTime, false, $e);
            
            Log::error("HighPerformanceImportJob: Import failed", [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2) . 's'
            ]);
            
            throw $e;
        } finally {
            $this->cleanup();
        }
    }

    /**
     * Initialize performance tracking
     */
    private function initializePerformanceTracking(): void
    {
        ImportPerformanceService::trackImportStart(
            $this->batchId,
            $this->importType,
            $this->userRole,
            $this->fileSizeBytes
        );
    }

    /**
     * Optimize environment for rocket-fast processing
     */
    private function optimizeEnvironment(): void
    {
        // Get optimal settings based on file size and role
        $settings = ImportPerformanceService::getOptimalSettings(
            $this->fileSizeBytes,
            $this->userRole
        );

        // Apply memory limit
        ini_set('memory_limit', $settings['memory_limit']);
        
        // Set execution time
        set_time_limit($settings['timeout']);
        
        // Optimize garbage collection
        gc_enable();
        
        Log::info("HighPerformanceImportJob: Environment optimized", $settings);
    }

    /**
     * Get optimal import class based on file size and type
     */
    private function getOptimalImportClass()
    {
        if ($this->importType === 'streaming' || $this->fileSizeBytes > 50 * 1024 * 1024) {
            return new StreamingUserImport($this->userRole, $this->batchId);
        }
        
        return new HighPerformanceUserImport($this->userRole, $this->batchId);
    }

    /**
     * Execute the import with monitoring
     */
    private function executeImport($importClass): void
    {
        $filePath = Storage::path($this->filePath);
        
        if (!file_exists($filePath)) {
            throw new \Exception("Import file not found: {$this->filePath}");
        }

        // Execute import with Excel facade
        Excel::import($importClass, $filePath);
    }

    /**
     * Track import completion
     */
    private function trackCompletion(float $executionTime, bool $success, ?\Exception $error = null): void
    {
        $memoryPeak = memory_get_peak_usage(true) / 1024 / 1024; // MB
        
        if ($success) {
            ImportPerformanceService::trackImportComplete(
                $this->batchId,
                0, // Will be updated by import class
                0, // Will be updated by import class
                0, // Will be updated by import class
                0, // Will be updated by import class
                $executionTime,
                (int) $memoryPeak,
                [
                    'job_class' => self::class,
                    'import_type' => $this->importType,
                    'user_id' => $this->userId,
                    'user_name' => $this->userName
                ]
            );
        } else {
            ImportPerformanceService::updateProgress(
                $this->batchId,
                0,
                0,
                1, // Mark as failed
                $executionTime,
                (int) $memoryPeak
            );
        }
    }

    /**
     * Get optimal queue for this import type
     */
    private function getOptimalQueue(): string
    {
        if ($this->importType === 'streaming' || $this->fileSizeBytes > 100 * 1024 * 1024) {
            return 'streaming_imports';
        } elseif ($this->fileSizeBytes > 10 * 1024 * 1024) {
            return 'high_performance_imports';
        }
        
        return 'default';
    }

    /**
     * Cleanup resources
     */
    private function cleanup(): void
    {
        // Delete temporary file if it exists
        if (Storage::exists($this->filePath)) {
            Storage::delete($this->filePath);
            Log::info("HighPerformanceImportJob: Cleaned up file", [
                'file_path' => $this->filePath
            ]);
        }
        
        // Force garbage collection
        gc_collect_cycles();
    }

    /**
     * Handle job failure
     */
    public function failed(\Exception $exception): void
    {
        Log::error("HighPerformanceImportJob: Job failed permanently", [
            'batch_id' => $this->batchId,
            'user_role' => $this->userRole,
            'file_path' => $this->filePath,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Track failure in performance metrics
        ImportPerformanceService::updateProgress(
            $this->batchId,
            0,
            0,
            1, // Failed
            0,
            0
        );

        // Cleanup on failure
        $this->cleanup();
    }

    /**
     * Get job tags for monitoring
     */
    public function tags(): array
    {
        return [
            'import',
            'excel',
            $this->userRole,
            $this->importType,
            "batch:{$this->batchId}"
        ];
    }

    /**
     * Calculate the number of seconds to wait before retrying
     */
    public function backoff(): array
    {
        return $this->backoff;
    }

    /**
     * Determine if the job should be retried based on the exception
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(6); // Retry for up to 6 hours
    }
}
