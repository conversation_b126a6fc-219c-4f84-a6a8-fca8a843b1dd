<?php

namespace App\Imports;

use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;

class SimpleHighPerformanceUserImport implements
    ToCollection,
    WithHeadingRow,
    WithChunkReading,
    WithBatchInserts
{
    protected $userRole;
    protected $batchID;
    protected $ip;
    protected $authUser;
    protected $processedRows = 0;
    protected $successfulRows = 0;
    protected $failedRows = 0;
    protected $startTime;

    public function __construct($userRole, $batchID = '', $ip = '')
    {
        $this->userRole = $userRole;
        $this->batchID = $batchID;
        $this->ip = $ip;

        // Get auth user safely
        try {
            $this->authUser = auth()->user();
        } catch (\Exception $e) {
            $this->authUser = null;
        }

        $this->startTime = microtime(true);

        // Initialize performance settings
        $this->initializePerformanceSettings();
    }

    /**
     * Initialize performance settings for rocket-fast processing
     */
    private function initializePerformanceSettings(): void
    {
        // Increase memory limit
        ini_set('memory_limit', '2048M');
        
        // Disable query logging for performance
        DB::disableQueryLog();
        
        // Enable garbage collection
        gc_enable();
    }

    /**
     * Process collection with rocket-fast bulk operations
     */
    public function collection(Collection $rows)
    {
        $totalRows = $rows->count();
        Log::info("SimpleHighPerformanceUserImport: Processing {$totalRows} rows for {$this->userRole}");

        // Debug: Log first row structure
        if ($totalRows > 0) {
            $firstRow = $rows->first();
            Log::info("SimpleHighPerformanceUserImport: First row structure", [
                'type' => get_class($firstRow),
                'keys' => is_array($firstRow) ? array_keys($firstRow) : array_keys($firstRow->toArray()),
                'sample_values' => is_array($firstRow) ? array_slice($firstRow, 0, 5) : array_slice($firstRow->toArray(), 0, 5)
            ]);
        }

        // Process in optimized batches
        $batchSize = 500; // Smaller batches for better performance
        $batches = $rows->chunk($batchSize);

        // Disable foreign key checks for faster inserts
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        foreach ($batches as $batch) {
            $this->processBatch($batch);
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->logPerformanceMetrics();
    }

    /**
     * Process batch with optimized bulk operations
     */
    private function processBatch(Collection $batch): void
    {
        $validRows = [];

        foreach ($batch as $row) {
            try {
                if ($this->isValidRow($row)) {
                    $userData = $this->prepareUserData($row);
                    if ($userData) {
                        $validRows[] = $userData;
                        $this->successfulRows++;
                    }
                } else {
                    $this->failedRows++;
                    Log::warning("SimpleHighPerformanceUserImport: Invalid row", ['row' => $row]);
                }
            } catch (\Exception $e) {
                $this->failedRows++;
                Log::error("SimpleHighPerformanceUserImport: Row processing error", [
                    'error' => $e->getMessage(),
                    'row' => $row
                ]);
            }
            $this->processedRows++;
        }

        // Bulk insert valid rows
        if (!empty($validRows)) {
            $this->bulkInsertUsers($validRows);
        }
    }

    /**
     * Validate row data
     */
    private function isValidRow($row): bool
    {
        $userIdField = $this->getUserIdField();

        if (!$userIdField || empty($row[$userIdField])) {
            Log::debug("SimpleHighPerformanceUserImport: Missing user ID field", [
                'user_id_field' => $userIdField,
                'row_keys' => array_keys($row),
                'user_role' => $this->userRole
            ]);
            return false;
        }

        // Basic required field validation
        $requiredFields = $this->getRequiredFields();
        foreach ($requiredFields as $field) {
            if (empty($row[$field])) {
                Log::debug("SimpleHighPerformanceUserImport: Missing required field", [
                    'field' => $field,
                    'value' => $row[$field] ?? 'NOT_SET',
                    'user_role' => $this->userRole
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Get user ID field for current role
     */
    private function getUserIdField(): ?string
    {
        $mapping = [
            "RSM" => "rsmrbdm_code",
            "ASM" => "asm_code",
            "DSR" => "dsr_code",
            "DISTRIBUTOR" => "distributor_code",
            "SO" => "so_user_code",
            "VENDOR" => "cfaplant_code",
            "RETAILER" => "retailercode",
            "VE" => "user_code",
            "HIERARCHY" => "rsmrbdm_code", // Default for hierarchy
        ];

        return $mapping[$this->userRole] ?? null;
    }

    /**
     * Get required fields for user type
     */
    private function getRequiredFields(): array
    {
        $fieldMap = [
            'RSM' => ['rsmrbdm_code', 'rsmrbdm_name'],
            'ASM' => ['asm_code', 'asm_name'],
            'SO' => ['so_user_code', 'so_name'],
            'DISTRIBUTOR' => ['distributor_code', 'distributor_name'],
            'DSR' => ['dsr_code', 'dsr_name'],
            'RETAILER' => ['retailercode', 'retailer_name'],
            'VE' => ['user_code'],
            'HIERARCHY' => ['rsmrbdm_code', 'rsmrbdm_name'],
        ];

        return $fieldMap[$this->userRole] ?? [];
    }

    /**
     * Prepare user data for insertion
     */
    private function prepareUserData($row): ?array
    {
        $userIdField = $this->getUserIdField();
        $userId = trim($row[$userIdField]);

        if (empty($userId)) {
            return null;
        }

        $baseData = [
            'user_id' => $userId,
            'user_type' => $this->userRole,
            'status' => 1,
            'password' => bcrypt($userId),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Add role-specific data
        $roleData = $this->getRoleSpecificData($row);
        return array_merge($baseData, $roleData);
    }

    /**
     * Get role-specific data mapping
     */
    private function getRoleSpecificData($row): array
    {
        switch ($this->userRole) {
            case 'RSM':
            case 'HIERARCHY':
                return [
                    'name' => trim($row['rsmrbdm_name'] ?? ''),
                    'region_code' => trim($row['rsm_region_code'] ?? ''),
                    'email' => trim($row['email_id'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'ASM':
                return [
                    'name' => trim($row['asm_name'] ?? ''),
                    'area_code' => trim($row['asm_area_code'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'email' => trim($row['email_id'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'SO':
                return [
                    'name' => trim($row['so_name'] ?? ''),
                    'teritory_code' => trim($row['so_teritory_code'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'mobile_number' => trim($row['so_contact_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'DISTRIBUTOR':
                return [
                    'name' => trim($row['distributor_name'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'so_id' => trim($row['so_user_code'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'DSR':
                return [
                    'name' => trim($row['dsr_name'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'so_id' => trim($row['so_user_code'] ?? ''),
                    'distributor_id' => trim($row['distributor_code'] ?? ''),
                    'mobile_number' => trim($row['dsr_contact_no'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'RETAILER':
                return [
                    'name' => trim($row['retailer_name'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'so_id' => trim($row['so_user_code'] ?? ''),
                    'distributor_id' => trim($row['distributor_code'] ?? ''),
                    'dsr_id' => trim($row['dsr_code'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'VE':
                return [
                    'name' => trim($row['user_name'] ?? $row['name'] ?? ''),
                    'email' => trim($row['email_id'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            default:
                return [];
        }
    }

    /**
     * Rocket-fast bulk insert with error handling
     */
    private function bulkInsertUsers(array $users): void
    {
        try {
            // Use raw SQL for maximum performance
            if (!empty($users)) {
                $columns = array_keys($users[0]);
                $placeholders = '(' . str_repeat('?,', count($columns) - 1) . '?)';
                $allPlaceholders = str_repeat($placeholders . ',', count($users) - 1) . $placeholders;

                $sql = "INSERT IGNORE INTO users (" . implode(',', $columns) . ") VALUES " . $allPlaceholders;

                $values = [];
                foreach ($users as $user) {
                    $values = array_merge($values, array_values($user));
                }

                DB::statement($sql, $values);
                Log::info("SimpleHighPerformanceUserImport: Bulk inserted " . count($users) . " users");
            }
        } catch (\Exception $e) {
            Log::error("SimpleHighPerformanceUserImport: Bulk insert failed", [
                'error' => $e->getMessage(),
                'count' => count($users)
            ]);

            // Fallback to Laravel's insertOrIgnore
            try {
                DB::table('users')->insertOrIgnore($users);
                Log::info("SimpleHighPerformanceUserImport: Fallback bulk insert successful");
            } catch (\Exception $e2) {
                Log::error("SimpleHighPerformanceUserImport: Fallback also failed", [
                    'error' => $e2->getMessage()
                ]);
                // Final fallback to individual inserts
                $this->fallbackIndividualInserts($users);
            }
        }
    }

    /**
     * Fallback to individual inserts if bulk insert fails
     */
    private function fallbackIndividualInserts(array $users): void
    {
        foreach ($users as $userData) {
            try {
                User::updateOrCreate(
                    ['user_id' => $userData['user_id'], 'user_type' => $userData['user_type']],
                    $userData
                );
            } catch (\Exception $e) {
                Log::error("SimpleHighPerformanceUserImport: Individual insert failed", [
                    'error' => $e->getMessage(),
                    'user_data' => $userData
                ]);
                $this->failedRows++;
                $this->successfulRows--;
            }
        }
    }

    /**
     * Log performance metrics
     */
    private function logPerformanceMetrics(): void
    {
        $endTime = microtime(true);
        $executionTime = $endTime - $this->startTime;
        $rowsPerSecond = $this->processedRows > 0 && $executionTime > 0 ? $this->processedRows / $executionTime : 0;

        Log::info("SimpleHighPerformanceUserImport: Performance Metrics", [
            'user_role' => $this->userRole,
            'batch_id' => $this->batchID,
            'processed_rows' => $this->processedRows,
            'successful_rows' => $this->successfulRows,
            'failed_rows' => $this->failedRows,
            'execution_time' => round($executionTime, 2) . 's',
            'rows_per_second' => round($rowsPerSecond, 2),
            'memory_peak' => memory_get_peak_usage(true) / 1024 / 1024 . 'MB'
        ]);
    }

    /**
     * Optimized chunk size for rocket-fast processing
     */
    public function chunkSize(): int
    {
        return 2000;
    }

    /**
     * Optimized batch size for bulk operations
     */
    public function batchSize(): int
    {
        return 1000;
    }



    /**
     * Cleanup method
     */
    public function __destruct()
    {
        // Force garbage collection
        gc_collect_cycles();
    }
}
