<?php

namespace App\Imports;

use App\Jobs\AssetInventoryUploadJob;
use App\Jobs\AssetPlacementApprovalHistoryJob;
use App\Jobs\AssetTaskCompletePlacementJob;
use App\Jobs\AssignAssetToPlacementJob;
use App\Jobs\DBToCFAVendorMappingJob;
use App\Jobs\ProcessAssetPlacementRequestJob;
use App\Jobs\ProcessFailedUpload;
use App\Jobs\ProcessUserImportRow;
use App\Jobs\UpdateRetailer;
use App\Jobs\UpdateUserRetailerGSTupdateJob;
use App\Library\Utils\DateFormate;
use App\Models\AssetPlacementRequest;
use App\Models\User;
use App\Services\AssetInventoryService;
use App\Services\ErrorService\ErrorHandlingService;
use App\Services\HeirarchyService;
use App\Services\PlacementRequestService\PlacementRequestService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Illuminate\Support\Facades\Log;
use App\Services\UserValidationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;
use Throwable;
use Illuminate\Contracts\Queue\ShouldQueue;

class UserMasterImport implements ToCollection, WithHeadingRow, WithChunkReading, WithBatchInserts, ShouldQueue
{
    protected $userRole;
    protected $batchID;
    protected $ip;

    public $totalRecords = 0;
    public $successRecords = 0;
    public $failedRecords = 0;
    public $failedData = [];
    protected $authUser;

    public function __construct($userRole, $batchID = '', $ip = '')
    {
        $this->userRole = $userRole;
        $this->batchID = $batchID;
        $this->ip = $ip;
        //        $this->authUser = Auth::user();
        $this->authUser = auth()->user();
    }

    public function collection(Collection $rows)
    {
        $authUser = $this->authUser;
        $users = [];
        $userValidationService = app(UserValidationService::class);
        $chunkSize = 1000;

        //        $this->totalRecords = count($rows);
        // print_r($rows->toArray());die;
        $rows->chunk($chunkSize)->each(function ($chunk) use ($userValidationService) {
            foreach ($chunk as $key => $row) {
                try {
                    if ($this->isEmptyRow($row)) {
                        continue; // Skip the row if it's empty
                    }
                    $this->totalRecords++;
                    if ($this->isValidRow($row, $userValidationService)) {
                        $this->successRecords++;
                        $prepareUserData = $this->prepareUserData($row);
                        if ($this->userRole == "db_to_cfa_mapping") {
                            #DB to CFA vendor mapping job
                            DBToCFAVendorMappingJob::dispatch($prepareUserData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
                        } elseif ($this->userRole == "retailer_lat_long") {
                            UpdateRetailer::dispatch($prepareUserData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
                        } elseif ($this->userRole == "retailer_gst_update") {
                            UpdateUserRetailerGSTupdateJob::dispatch($prepareUserData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
                        } elseif ($this->userRole == "asset_inventory_upload") {

                            $loginUser = Auth::user();
                            $userType = $loginUser->user_type;
                            $userID = $loginUser->user_id;
                            $prepareUserData['batchID'] = $this->batchID;
                            $prepareUserData['uploadBy'] = $userID;
                            $prepareUserData['uploadByName'] = $loginUser->name;

                            #validate Asset Upload data
                            $assetValidate = AssetInventoryService::validateAsset($prepareUserData);
                            $prepareUserData['asset_type'] =  $assetValidate['asset_type'];
                            $prepareUserData['asset_code'] =  $assetValidate['asset_code'];
                            $prepareUserData['uploadTime'] = now();
                            AssetInventoryUploadJob::dispatch($prepareUserData, $userType, $userID, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
                        } elseif ($this->userRole == "asset_retailer_mapping") {
                            DB::beginTransaction();
                            try {
                                if (empty($prepareUserData['request_number'])) {
                                    $request_number = PlacementRequestService::generateRequestNumber();
                                    $prepareUserData['request_number'] = $request_number;
                                }

                                #validate Asset Upload data
                                $assetValidate = AssetInventoryService::placementAssetTypeCode($prepareUserData['eligible_chiller_type']);
                                $prepareUserData['asset_type_code'] =  $assetValidate['asset_type'];
                                $prepareUserData['eligible_chiller_type'] =  $assetValidate['asset_code'];

                                if (AssetPlacementRequest::where('request_number', $prepareUserData['request_number'])->exists()) {
                                    throw new \Exception("Placement request with this request number already exists.");
                                }

                                #check pendign from and request status valid
                                $pendingFrom = ['AE', 'RSM', 'ASM', 'VENDOR', 'VE'];
                                $request_pending_from = strtoupper($prepareUserData['request_pending_from']); // Assuming this is how you get the value
                                $request_Status_list = ['deployed', 'rejected', 'cancelled', 'pending'];
                                $request_Status =  strtolower($prepareUserData['request_status']);

                                if (!in_array($request_pending_from, $pendingFrom)) { // Check if the value is NOT in the list
                                    throw new \Exception("Invalid Pending From");
                                }
                                if (!in_array($request_Status, $request_Status_list)) {
                                    throw new \Exception("Ivalid Request Status");
                                }
                                if ($request_Status == 'deployed') {
                                    if (empty($prepareUserData['asset_serial_number']) || empty($prepareUserData['asset_barcode'])) {
                                        throw new \Exception("Asset Serial Nuber OR Asset barcode is Required");
                                    }
                                }
                                HeirarchyService::validateHeirarchy($prepareUserData, $this->userRole);
                                #handle validation asset type
                                $assetValidate = AssetInventoryService::placementAssetTypeCode($prepareUserData['eligible_chiller_type']);
                                $prepareUserData['eligible_chiller_type'] =  $assetValidate['asset_type'];
                                $prepareUserData['asset_type_code'] =  $assetValidate['asset_code'];

                                #handle asset already exist
                                if ($request_Status == 'deployed') {
                                    $serialBarcodeExist = AssetPlacementRequest::where('asset_serial_number', $prepareUserData['asset_serial_number'])
                                        ->when(!empty($prepareUserData['asset_barcode']), function ($query) use ($prepareUserData) {
                                            return $query->orWhere('asset_barcode', $prepareUserData['asset_barcode']);
                                        })->exists();

                                    if ($serialBarcodeExist) {
                                        throw new \Exception(" Asset Serial Number/BarCode Already Exist");
                                    }
                                }
                                $user = User::where('user_id', $prepareUserData['outlet_code'])->with('outletHasOne')->first();
                                Log::info('Handling ProcessAssetPlacementRequestJob', $prepareUserData);
                                $this->processPlacementRequest($prepareUserData, $user);
                                AssetPlacementApprovalHistoryJob::dispatch($prepareUserData, $user);
                                $this->processTaskCompletePlacementRequest($prepareUserData, $user);
                                // if ($prepareUserData['request_status'] == 'deployed') {
                                if ($request_Status == 'deployed') {
                                    AssignAssetToPlacementJob::dispatch($prepareUserData, $user, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
                                }
                                DB::commit();
                            } catch (\Exception $e) {
                                $this->failedRecords++;
                                $this->failedData[] = $prepareUserData;
                                DB::rollBack();
                                $this->failedDataErrorLog($e->getMessage(), $prepareUserData);
                                continue;
                            }
                        } else {
                            try {
                                $userId = $this->getUserID($row);
                                $userRole = $this->userRole == "CFA" ? "VENDOR" : $this->userRole;
                                if (empty($userId)) {
                                    throw new \Exception($this->userRole . " Code is missing .");
                                }
                                $userData = [
                                    'user_id' => $userId,
                                    'user_type' => $userRole,
                                    'email' => $row['email_id'] ?? '',
                                    'mobile_number' => $row['mobile_number'] ?? '',
                                    'rsm_id' => $prepareUserData['rsm_id'] ?? '',
                                    'asm_id' => $prepareUserData['asm_id'] ?? '',
                                    'so_id' => $prepareUserData['so_id'] ?? '',
                                    'distributor_id' => $prepareUserData['distributor_id'] ?? '',
                                    'dsr_id' => $prepareUserData['dsr_id'] ?? '',
                                    'rtmm_code' => $prepareUserData['rtmm_code'] ?? '',
                                    'ae_id' => $prepareUserData['ae_id'] ?? 'AE_001',
                                    'cfa_code' => $prepareUserData['cfa_code'] ?? '',
                                    'batchID' => $this->batchID,
                                    'uploadBy' => $authUser->user_id ?? '',
                                    'uploadByName' => $authUser->name ?? '',
                                    'uploadTime' => now(),
                                ];

                                if ($this->userRole != 'RETAILER') {
                                    $userData['password'] = bcrypt($userId);
                                }
                                HeirarchyService::validateHeirarchy($userData, $userRole);
                                $userData = array_merge($userData, $prepareUserData);
                                ProcessUserImportRow::dispatch($userId, $userRole, $userData, $prepareUserData, $row, $this->authUser, request()->ip());
                            } catch (\Exception $e) {
                                $this->failedRecords++;
                                $this->failedData[] = $row->toArray();
                                $this->failedDataErrorLog($e->getMessage(), $row->toArray());
                                Log::error('ProcessUserImportRow:', $row->toArray());
                            }
                        }
                    }
                    //                    else {
                    //                        $this->failedRecords++;
                    //                        $this->failedData[]=$row->toArray();
                    //                        $this->failedDataErrorLog('validation_failed', $row->toArray());
                    //                        Log::error('validation failed user:', $row->toArray());
                    //                    }
                } catch (QueryException $e) {
                    // Handle the specific database error
                    $this->failedDataErrorLog($e->getMessage(), $row->toArray());
                    Log::error('Error updating user: ' . $e->getMessage(), ['row_data' => $row->toArray()]);
                } catch (\Exception $e) {
                    $this->failedRecords++;
                    $this->failedData[] = $row->toArray();
                    $this->failedDataErrorLog($e->getMessage(), $row->toArray());
                    Log::error('Error updating user: ' . $e->getMessage(), ['row_data' => $row->toArray()]);
                }
            }
        });
        $this->logUploadHistory($this->batchID);
        return $users;
    }


    private function isEmptyRow($row)
    {
        foreach ($row as $value) {
            if (!empty($value)) {
                return false; // If any value is not empty, return false
            }
        }
        return true; // All values are empty
    }

    private function isValidRow($row, UserValidationService $userValidationService)
    {
        try {
            $requiredFields = $userValidationService->getUserTypeFields();

            if ($row->filter()->isEmpty()) {
                Log::error('isValidRow => Validation failed for row: All columns are empty.', $row->toArray());

                return false;
            }

            $missingFields = collect($requiredFields['validate'] ?? [])->filter(function ($field) use ($row) {
                return !isset($row[$field]) || empty($row[$field]);
            })->all();


            if (!empty($missingFields)) {
                throw new \Exception('Validation failed for row: Missing columns - ' . implode(', ', $missingFields));
                //                $this->failedRecords++;
                //                $this->failedDataErrorLog('Validation failed for row: Missing columns - ' . implode(', ', $missingFields), $row);
                //                Log::error('Validation failed for row: Missing columns - ' . implode(', ', $missingFields));
                //                return false;
            }
            $requiredFields = $userValidationService->getUserTypeFields()[$this->userRole]['validate'] ?? [];
            $requiredMissingFields = [];
            foreach ($requiredFields as $field) {
                if (empty($row[$field])) {
                    $requiredMissingFields[] = $field;
                }
            }
            if (!empty($requiredMissingFields)) {
                throw new \Exception('Validation failed: Missing or empty fields - ' . implode(', ', $requiredMissingFields));
            }

            return true;
        } catch (\Exception $e) {
            $this->failedRecords++;
            $this->failedDataErrorLog($e->getMessage(), $row);
            Log::error('Error validating row: ' . $e->getMessage());
            return false;
        }
    }

    private function prepareUserData($row)
    {
        $userData = [];

        $fieldMappings = app(UserValidationService::class)->getUserTypeFields()[$this->userRole]['form'] ?? [];

        foreach ($fieldMappings as $excelField) {
            $databaseField = app(UserValidationService::class)->mapExcelToDatabaseFields($this->userRole, $excelField);
            if ($databaseField && isset($row[$excelField])) {
                // $userData[$databaseField] = (string) trim($row[$excelField]);
                if (in_array($databaseField, ['effective_from', 'effective_to', 'retailer_created_on', 'modified_date'])) {
                    // $userData[$databaseField] = now();
                    // $userData[$databaseField] = Carbon::createFromFormat('Y-m-d', $row[$excelField])->format('Y-m-d');
                    $userData[$databaseField] = $this->convertDateFormatToDbFormat($row[$excelField]);
                } else {
                    // $userData[$databaseField] = $row[$excelField];
                    $userData[$databaseField] = (string) trim($row[$excelField]);
                }
            }
        }
        return $userData;
    }

    private function getUserID($row)
    {
        $userIDList = [
            "RSM" => "rsmrbdm_code",
            "ASM" => "asm_code",
            "DSR" => "dsr_code",
            "DISTRIBUTOR" => "distributor_code",
            "SO" => "so_user_code",
            "VENDOR" => "cfaplant_code",
            "RETAILER" => "retailercode",
            "VE" => "user_code",
        ];

        $userId = $row[$userIDList[$this->userRole]] ?? null;

        // if (!$userId) {
        //     $userId = UserIDGeneratorService::generateUserID($this->userRole);
        // }

        return $userId;
    }

    private function convertDateFormatToDbFormat($excelField, $timestamp = '')
    {
        $format = $timestamp == 'time' ? 'Y-m-d H:i:s' : '';
        return DateFormate::convertDbFormat($excelField, $format);
    }


    public function chunkSize(): int
    {
        // Large chunk size for high performance with big files
        return 5000;
    }

    public function batchSize(): int
    {
        // Large batch size for efficient DB inserts
        return 5000;
    }


    public function processPlacementRequest($amprocess, $user)
    {

        $amprocess['request_status'] = strtolower($amprocess['request_status']);
        $amprocess['request_pending_from'] = strtoupper($amprocess['request_pending_from']);
        $aprData = [
            'user_id' => $amprocess['so_code'],
            'asset_type_code' => $amprocess['asset_type_code'],
            'outlet_id' => $user->outletHasOne->id ?? '',
            'type' => 'SO',
            'expected_vpo' => $amprocess['expected_vpo'] ?? '',
            'request_number' => $amprocess['request_number'] ?? '',
            'eligible_chiller_type' => $amprocess['eligible_chiller_type'] ?? null,
            'request_type' => 'Normal',
            // 'additional_equipment' => json_encode([]),
            // 'competitor_chiller_size' => json_encode([]),
            // 'competitor_company' => json_encode([]),
            'chiller_location' => 'Others',
            // 'chiller_location_photo' => 'NULL',
            'customer_address' => $amprocess['customer_address'] ?? '',
            'customer_location' => $amprocess['customer_location'] ?? '',
            // 'current_location' => $amprocess['current_location'],
            // 'latitude' => $user->outletHasOne->lat ?? 0.0,
            // 'longitude' => $user->outletHasOne->long ?? 0.0,
            'correct_location' => $amprocess['correct_location'] ?? 'No',
            'remarks' => 'ivy asset mapping =>' . now(),
            'asset_mapping_type' => 'ivy',
            'consent_status' => 'Confirmed',
            'vpo_target' => $amprocess['vpo'],
            "outlet_code" => $amprocess['outlet_code'],
            "ae_code" => $amprocess['ae_code'],
            "rsm_code" => $amprocess['rsm_code'],
            "asm_code" => $amprocess['asm_code'],
            "so_code" => $amprocess['so_code'],
            "db_code" => $amprocess['db_code'],
            "dsr_code" => $amprocess['dsr_code'],
            "cfa_code" => $amprocess['cfa_code'],
            "asset_serial_number" => $amprocess['asset_serial_number'] ?? '',
            "asset_barcode" => $amprocess['asset_barcode'] ?? '',
            "asset_assigned_status" => 'Approved',
            "address_proof" => 'NULL',
            "retailer_photo" => 'NULL',
            "mobile_number" => 'NULL',
            "pincode" => $user->outletHasOne->pincode ?? '',
            "signature" => 'NULL',
            "approved_time" => now(),
            "placement_approved_time" => $amprocess['request_status'] != 'rejected' ? now() : null,
            "deployment_date" => isset($amprocess['deployment_date']) ? $this->convertDateFormatToDbFormat($amprocess['deployment_date'], 'time') : now(),
            // 'approved_by_user_id' => $amprocess['cfa_code'] ?? '',
            // 'approved_by_user_role' => 'VENDOR',
            // 'pending_from' => 'VE',
            // 'is_deploy' => 1,
            // 'is_quantity_allocated' => 'YES',
            'expected_deployment_date' => isset($amprocess['deployment_date']) ? $this->convertDateFormatToDbFormat($amprocess['deployment_date'], 'time') : now(),
            'assigned_organization' => $amprocess['cfa_code'] . "_001",
            // 'chiller_placed' => 'Yes',
            // 'task_status' => 'Completed',
            'pending_from' => $amprocess['request_pending_from'] ?? 'ASM',
            'batchID' => $this->batchID,
            'uploadBy' => $this->authUser->user_id ?? '',
            'uploadByName' => $this->authUser->name ?? '',
            'uploadTime' => now(),
        ];

        if ($amprocess['request_status'] == 'deployed') {
            $aprData['approved_by_user_id'] = !empty($amprocess['cfa_code']) ? $amprocess['cfa_code'] . '_001' : '';
            $aprData['approved_by_user_role'] = 'VENDOR';
            $aprData['pending_from'] = 'VE';
            $aprData['is_deploy'] = 1;
            $aprData['chiller_placed'] = 'Yes';
            $aprData['task_status'] = 'Completed';
            $aprData['is_quantity_allocated'] = 'YES';
        } elseif ($amprocess['request_status'] == 'rejected') {
            $aprData['chiller_placed'] = 'No';
            $aprData['task_status'] = 'Pending';
            $aprData['is_deploy'] = 0;
            $aprData['rejected_by_user_id'] = $this->getHeirarchyUserID($amprocess['request_pending_from'], $amprocess);
            $aprData['rejected_by_user_role'] = $amprocess['request_pending_from'];
            $aprData['placement_rejected_time'] = now();
            $aprData['approved_by_user_id'] = '';
            $aprData['approved_by_user_role'] = '';
            $aprData['is_quantity_allocated'] = 'NO';
            $aprData['rejection_reason'] = 'IVY cancelled this request and upload from admin user master, date: ' . now();
        } elseif ($amprocess['request_status'] == 'cancelled') {
            $aprData['approved_by_user_id'] = $amprocess['cfa_code'] ?? '';
            $aprData['approved_by_user_role'] = 'VENDOR';
            $aprData['pending_from'] = 'VE';
            $aprData['is_deploy'] = 0;
            $aprData['chiller_placed'] = 'No';
            $aprData['task_status'] = 'Completed';
            $aprData['is_quantity_allocated'] = 'NO';
        } else {
            $aprData['approved_by_user_id'] = $this->getHeirarchyUserID($amprocess['request_pending_from'], $amprocess);
            $aprData['approved_by_user_role'] = $amprocess['request_pending_from'];
            $aprData['pending_from'] = $amprocess['request_pending_from'] ?? 'ASM';
            $aprData['is_deploy'] = 0;
            $aprData['chiller_placed'] = 'No';
            $aprData['task_status'] = 'Pending';
            $aprData['is_quantity_allocated'] = 'NO';
        }
        ProcessAssetPlacementRequestJob::dispatch($aprData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
    }

    public function getHeirarchyUserID($pendingFrom, $heirarchy)
    {
        if (!isset($pendingFrom) || !is_array($heirarchy)) {
            return '';
        }
        $data = [
            'ASM' => $heirarchy['asm_code'],
            'RSM' => $heirarchy['rsm_code'],
            'AE' => $heirarchy['ae_code'],
            'VENDOR' => $heirarchy['cfa_code'],
            'VE' => $heirarchy['cfa_code']
        ];
        return $data[$pendingFrom] ?? '';
    }

    public function processTaskCompletePlacementRequest($amprocess, $user)
    {
        $outlet = $user->outletHasOne ?? null;

        $latitude = $outlet ? $outlet->lat : null;
        $longitude = $outlet ? $outlet->long : null;
        $chillerPlaced = $amprocess['request_status'] == 'deployed' ? 'Yes' : 'No';
        $aprData = [
            'asset_barcode' => $amprocess['asset_barcode'] ?? '',
            'asset_number' => $amprocess['asset_serial_number'] ?? '',
            'chiller_placed' => $chillerPlaced,
            'is_Deploy' => 0,
            'correct_location' => 'Yes',
            'pending_from' => 'VE',
            'current_location' => $amprocess['customer_address'],
            'latitude' => $latitude,
            'longitude' => $longitude,
            'outlet_id' => optional($user->outletHasOne)->id ?? '',
            'remarks' => 'ivy task completed',
            'request_number' => $amprocess['request_number'] ?? '',
            'user_id' => $amprocess['cfa_code'] . "_001",
            'approved_by_user_role' => 'VE',
            'task_status' => 'Pending',
            'approved_by_user_id' => $amprocess['cfa_code'] . "_001",
            'outlet_code' => $amprocess['outlet_code'],
            "ae_code" => $amprocess['ae_code'],
            "rsm_code" => $amprocess['rsm_code'],
            "asm_code" => $amprocess['asm_code'],
            "so_code" => $amprocess['so_code'],
            "db_code" => $amprocess['db_code'],
            "dsr_code" => $amprocess['dsr_code'],
            "cfa_code" => $amprocess['cfa_code'],
            "completion_date" => now(),
            'batchID' => $this->batchID,
            'uploadBy' => $this->authUser->user_id ?? '',
            'uploadByName' => $this->authUser->name ?? '',
            'uploadTime' => now(),
        ];
        Log::info('Handling processTaskCompletePlacementRequest', $aprData);
        AssetTaskCompletePlacementJob::dispatch($aprData, request()->ip(), $this->authUser, $this->userRole, $this->batchID);
    }

    public function failedDataErrorLog($message, $prepareUserData)
    {
        $authUser = $this->authUser;
        ErrorHandlingService::handleErrorLog($this->batchID, $message, request()->ip(), $authUser->user_type, $this->userRole, $prepareUserData, $authUser->user_id, $authUser->name);
    }

    private function logUploadHistory($batchID)
    {
        $authUser = $this->authUser;
        ErrorHandlingService::logUploadHistory($batchID, $this->totalRecords, $this->successRecords, $this->failedRecords, $this->failedData, $authUser->user_id, $authUser->name, request()->ip());
    }
}
