<?php

namespace App\Imports;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\RemembersRowNumber;

/**
 * Ultra-high performance streaming import for massive Excel files
 * Optimized for files with 100K+ rows
 */
class StreamingUserImport implements
    ToModel,
    WithHeadingRow,
    WithChunkReading,
    WithBatchInserts,
    SkipsEmptyRows,
    ShouldQueue
{
    use RemembersRowNumber, SkipsErrors, SkipsFailures;

    protected $userRole;
    protected $batchID;
    protected $ip;
    protected $authUser;
    protected $batchBuffer = [];
    protected $bufferSize;
    protected $processedCount = 0;
    protected $startTime;

    // Performance tracking
    protected $metrics = [
        'total_processed' => 0,
        'successful_inserts' => 0,
        'failed_inserts' => 0,
        'memory_peak' => 0,
        'execution_time' => 0
    ];

    public function __construct($userRole, $batchID = '', $ip = '')
    {
        $this->userRole = $userRole;
        $this->batchID = $batchID;
        $this->ip = $ip;

        // Get auth user safely
        try {
            $this->authUser = auth()->user();
        } catch (\Exception $e) {
            $this->authUser = null;
        }

        $this->bufferSize = config('excel.import_optimizations.batch_size', 1000);
        $this->startTime = microtime(true);

        $this->initializeStreamingOptimizations();
    }

    /**
     * Initialize streaming optimizations for maximum performance
     */
    private function initializeStreamingOptimizations(): void
    {
        // Set unlimited execution time for large files
        set_time_limit(0);
        
        // Increase memory limit
        ini_set('memory_limit', '4096M');
        
        // Optimize MySQL for bulk operations
        DB::statement('SET SESSION sql_mode = ""');
        DB::statement('SET SESSION innodb_autoinc_lock_mode = 2');
        DB::statement('SET SESSION unique_checks = 0');
        DB::statement('SET SESSION foreign_key_checks = 0');
        DB::statement('SET SESSION autocommit = 0');
        
        // Disable query logging
        DB::disableQueryLog();
        
        Log::info("StreamingUserImport: Initialized for {$this->userRole} with buffer size {$this->bufferSize}");
    }

    /**
     * Process each row with streaming approach
     */
    public function model(array $row)
    {
        // Skip empty rows
        if ($this->isEmptyRow($row)) {
            return null;
        }

        // Validate and prepare data
        $userData = $this->prepareUserData($row);
        if (!$userData) {
            $this->metrics['failed_inserts']++;
            return null;
        }

        // Add to buffer
        $this->batchBuffer[] = $userData;
        
        // Process buffer when full
        if (count($this->batchBuffer) >= $this->bufferSize) {
            $this->flushBuffer();
        }

        $this->processedCount++;
        
        // Log progress every 10,000 rows
        if ($this->processedCount % 10000 === 0) {
            $this->logProgress();
        }

        return null; // We handle inserts manually for better performance
    }

    /**
     * Check if row is empty
     */
    private function isEmptyRow(array $row): bool
    {
        return empty(array_filter($row, function($value) {
            return !empty(trim($value));
        }));
    }

    /**
     * Prepare user data with validation
     */
    private function prepareUserData(array $row): ?array
    {
        $userIdField = $this->getUserIdField();
        if (!$userIdField || empty($row[$userIdField])) {
            return null;
        }

        $userId = trim($row[$userIdField]);
        
        // Basic validation
        if (strlen($userId) < 3) {
            return null;
        }

        $userData = [
            'user_id' => $userId,
            'user_type' => $this->userRole,
            'status' => 1,
            'password' => bcrypt($userId),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Add role-specific fields
        $roleData = $this->getRoleSpecificData($row);
        return array_merge($userData, $roleData);
    }

    /**
     * Get user ID field for current role
     */
    private function getUserIdField(): ?string
    {
        $mapping = [
            "RSM" => "rsmrbdm_code",
            "ASM" => "asm_code",
            "DSR" => "dsr_code",
            "DISTRIBUTOR" => "distributor_code",
            "SO" => "so_user_code",
            "VENDOR" => "cfaplant_code",
            "RETAILER" => "retailercode",
            "VE" => "user_code",
        ];

        return $mapping[$this->userRole] ?? null;
    }

    /**
     * Get role-specific data
     */
    private function getRoleSpecificData(array $row): array
    {
        switch ($this->userRole) {
            case 'RSM':
                return [
                    'name' => trim($row['rsmrbdm_name'] ?? ''),
                    'region_code' => trim($row['rsm_region_code'] ?? ''),
                    'email' => trim($row['email_id'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'ASM':
                return [
                    'name' => trim($row['asm_name'] ?? ''),
                    'area_code' => trim($row['asm_area_code'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'email' => trim($row['email_id'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'SO':
                return [
                    'name' => trim($row['so_name'] ?? ''),
                    'teritory_code' => trim($row['so_teritory_code'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'mobile_number' => trim($row['so_contact_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'DISTRIBUTOR':
                return [
                    'name' => trim($row['distributor_name'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'so_id' => trim($row['so_user_code'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'DSR':
                return [
                    'name' => trim($row['dsr_name'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'so_id' => trim($row['so_user_code'] ?? ''),
                    'distributor_id' => trim($row['distributor_code'] ?? ''),
                    'mobile_number' => trim($row['dsr_contact_no'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            case 'RETAILER':
                return [
                    'name' => trim($row['retailer_name'] ?? ''),
                    'rsm_id' => trim($row['rsm_code'] ?? ''),
                    'asm_id' => trim($row['asm_code'] ?? ''),
                    'so_id' => trim($row['so_user_code'] ?? ''),
                    'distributor_id' => trim($row['distributor_code'] ?? ''),
                    'dsr_id' => trim($row['dsr_code'] ?? ''),
                    'mobile_number' => trim($row['mobile_number'] ?? ''),
                    'region' => trim($row['region_name'] ?? ''),
                    'city' => trim($row['city'] ?? ''),
                    'state' => trim($row['state'] ?? ''),
                    'pin_code' => trim($row['pincode'] ?? ''),
                    'cfa_code' => trim($row['cfa_code'] ?? ''),
                ];

            default:
                return [];
        }
    }

    /**
     * Flush buffer with ultra-fast bulk insert
     */
    private function flushBuffer(): void
    {
        if (empty($this->batchBuffer)) {
            return;
        }

        try {
            // Use raw SQL for maximum performance
            $this->bulkInsertRaw($this->batchBuffer);
            
            $this->metrics['successful_inserts'] += count($this->batchBuffer);
            
            Log::debug("StreamingUserImport: Flushed buffer", [
                'count' => count($this->batchBuffer),
                'total_processed' => $this->processedCount
            ]);
            
        } catch (\Exception $e) {
            Log::error("StreamingUserImport: Buffer flush failed", [
                'error' => $e->getMessage(),
                'count' => count($this->batchBuffer)
            ]);
            
            $this->metrics['failed_inserts'] += count($this->batchBuffer);
        }

        // Clear buffer and force garbage collection
        $this->batchBuffer = [];
        if (memory_get_usage() > 500 * 1024 * 1024) { // 500MB
            gc_collect_cycles();
        }
    }

    /**
     * Ultra-fast raw SQL bulk insert
     */
    private function bulkInsertRaw(array $data): void
    {
        if (empty($data)) {
            return;
        }

        $columns = array_keys($data[0]);
        $placeholders = '(' . str_repeat('?,', count($columns) - 1) . '?)';
        $allPlaceholders = str_repeat($placeholders . ',', count($data) - 1) . $placeholders;
        
        $sql = "INSERT INTO users (" . implode(',', $columns) . ") VALUES " . $allPlaceholders . 
               " ON DUPLICATE KEY UPDATE " . 
               implode(',', array_map(fn($col) => "$col = VALUES($col)", $columns));

        $values = [];
        foreach ($data as $row) {
            $values = array_merge($values, array_values($row));
        }

        DB::statement($sql, $values);
    }

    /**
     * Log progress for monitoring
     */
    private function logProgress(): void
    {
        $currentTime = microtime(true);
        $elapsed = $currentTime - $this->startTime;
        $rate = $this->processedCount / $elapsed;
        $memoryUsage = memory_get_usage(true) / 1024 / 1024;

        Log::info("StreamingUserImport: Progress Update", [
            'processed' => $this->processedCount,
            'rate_per_second' => round($rate, 2),
            'elapsed_time' => round($elapsed, 2) . 's',
            'memory_usage' => round($memoryUsage, 2) . 'MB',
            'buffer_size' => count($this->batchBuffer)
        ]);
    }

    /**
     * Optimized chunk size for streaming
     */
    public function chunkSize(): int
    {
        return 5000; // Larger chunks for streaming
    }

    /**
     * Batch size for bulk operations
     */
    public function batchSize(): int
    {
        return $this->bufferSize;
    }

    /**
     * Cleanup and final flush
     */
    public function __destruct()
    {
        // Flush any remaining data
        if (!empty($this->batchBuffer)) {
            $this->flushBuffer();
        }

        // Restore MySQL settings
        DB::statement('SET SESSION unique_checks = 1');
        DB::statement('SET SESSION foreign_key_checks = 1');
        DB::statement('SET SESSION autocommit = 1');
        
        // Final metrics
        $this->metrics['execution_time'] = microtime(true) - $this->startTime;
        $this->metrics['memory_peak'] = memory_get_peak_usage(true) / 1024 / 1024;
        $this->metrics['total_processed'] = $this->processedCount;

        Log::info("StreamingUserImport: Final Metrics", $this->metrics);
    }
}
