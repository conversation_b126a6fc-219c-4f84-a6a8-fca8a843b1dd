<?php

namespace App\Imports;

use App\Jobs\ProcessUserImportBatch;
use App\Models\User;
use App\Services\UserValidationService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithUpserts;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\RemembersRowNumber;

class HighPerformanceUserImport implements 
    ToCollection, 
    WithHeadingRow, 
    WithChunkReading, 
    WithBatchInserts,
    WithUpserts,
    SkipsEmptyRows,
    SkipsErrors,
    SkipsFailures,
    ShouldQueue
{
    use RemembersRowNumber;

    protected $userRole;
    protected $batchID;
    protected $ip;
    protected $authUser;
    protected $processedRows = 0;
    protected $totalRows = 0;
    protected $startTime;

    // Performance optimization: Pre-compiled validation rules
    protected static $validationRules = [];
    protected static $userTypeMapping = [];

    public function __construct($userRole, $batchID = '', $ip = '')
    {
        $this->userRole = $userRole;
        $this->batchID = $batchID;
        $this->ip = $ip;
        $this->authUser = auth()->user();
        $this->startTime = microtime(true);
        
        // Initialize performance optimizations
        $this->initializePerformanceSettings();
        $this->preloadCacheData();
    }

    /**
     * Initialize performance settings for rocket-fast processing
     */
    private function initializePerformanceSettings(): void
    {
        // Increase memory limit
        ini_set('memory_limit', config('excel.import_optimizations.memory_limit', '2048M'));
        
        // Disable query logging for performance
        if (config('excel.import_optimizations.disable_query_log', true)) {
            DB::disableQueryLog();
        }
        
        // Disable foreign key checks for faster inserts
        if (config('excel.import_optimizations.disable_foreign_keys', true)) {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        }
        
        // Optimize garbage collection
        if (config('excel.performance.gc_collect_cycles', true)) {
            gc_enable();
        }
    }

    /**
     * Preload frequently accessed data into cache
     */
    private function preloadCacheData(): void
    {
        // Cache user type mappings
        if (empty(self::$userTypeMapping)) {
            self::$userTypeMapping = Cache::remember('user_type_mapping', 3600, function () {
                return [
                    "RSM" => "rsmrbdm_code",
                    "ASM" => "asm_code", 
                    "DSR" => "dsr_code",
                    "DISTRIBUTOR" => "distributor_code",
                    "SO" => "so_user_code",
                    "VENDOR" => "cfaplant_code",
                    "RETAILER" => "retailercode",
                    "VE" => "user_code",
                ];
            });
        }
    }

    /**
     * Process collection with rocket-fast bulk operations
     */
    public function collection(Collection $rows)
    {
        $this->totalRows = $rows->count();
        Log::info("HighPerformanceUserImport: Processing {$this->totalRows} rows for {$this->userRole}");

        // Process in optimized batches
        $batchSize = config('excel.import_optimizations.batch_size', 1000);
        $batches = $rows->chunk($batchSize);

        DB::transaction(function () use ($batches) {
            foreach ($batches as $batch) {
                $this->processBatch($batch);
            }
        });

        $this->logPerformanceMetrics();
    }

    /**
     * Process batch with optimized bulk operations
     */
    private function processBatch(Collection $batch): void
    {
        $validRows = [];
        $invalidRows = [];

        // Bulk validation - much faster than row-by-row
        foreach ($batch as $index => $row) {
            if ($this->isValidRowFast($row)) {
                $validRows[] = $this->prepareUserDataFast($row);
            } else {
                $invalidRows[] = ['row' => $row, 'index' => $index];
            }
        }

        // Bulk insert valid rows
        if (!empty($validRows)) {
            $this->bulkInsertUsers($validRows);
        }

        // Handle invalid rows
        if (!empty($invalidRows)) {
            $this->handleInvalidRows($invalidRows);
        }

        $this->processedRows += count($batch);
    }

    /**
     * Ultra-fast row validation
     */
    private function isValidRowFast($row): bool
    {
        $userIdField = self::$userTypeMapping[$this->userRole] ?? null;
        
        if (!$userIdField || empty($row[$userIdField])) {
            return false;
        }

        // Basic required field validation
        $requiredFields = $this->getRequiredFields();
        foreach ($requiredFields as $field) {
            if (empty($row[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get required fields for user type
     */
    private function getRequiredFields(): array
    {
        $fieldMap = [
            'RSM' => ['rsmrbdm_code', 'rsmrbdm_name'],
            'ASM' => ['asm_code', 'asm_name'],
            'SO' => ['so_user_code', 'so_name'],
            'DISTRIBUTOR' => ['distributor_code', 'distributor_name'],
            'DSR' => ['dsr_code', 'dsr_name'],
            'RETAILER' => ['retailercode', 'retailer_name'],
        ];

        return $fieldMap[$this->userRole] ?? [];
    }

    /**
     * Fast data preparation
     */
    private function prepareUserDataFast($row): array
    {
        $userIdField = self::$userTypeMapping[$this->userRole];
        $userId = $row[$userIdField];

        $baseData = [
            'user_id' => $userId,
            'user_type' => $this->userRole,
            'status' => 1,
            'password' => bcrypt($userId),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Add role-specific data
        return array_merge($baseData, $this->getRoleSpecificData($row));
    }

    /**
     * Get role-specific data mapping
     */
    private function getRoleSpecificData($row): array
    {
        switch ($this->userRole) {
            case 'RSM':
                return [
                    'name' => $row['rsmrbdm_name'] ?? '',
                    'region_code' => $row['rsm_region_code'] ?? '',
                    'email' => $row['email_id'] ?? '',
                    'mobile_number' => $row['mobile_number'] ?? '',
                ];
            case 'ASM':
                return [
                    'name' => $row['asm_name'] ?? '',
                    'area_code' => $row['asm_area_code'] ?? '',
                    'rsm_id' => $row['rsm_code'] ?? '',
                    'email' => $row['email_id'] ?? '',
                    'mobile_number' => $row['mobile_number'] ?? '',
                ];
            case 'SO':
                return [
                    'name' => $row['so_name'] ?? '',
                    'teritory_code' => $row['so_teritory_code'] ?? '',
                    'rsm_id' => $row['rsm_code'] ?? '',
                    'asm_id' => $row['asm_code'] ?? '',
                    'mobile_number' => $row['so_contact_number'] ?? '',
                ];
            case 'DISTRIBUTOR':
                return [
                    'name' => $row['distributor_name'] ?? '',
                    'rsm_id' => $row['rsm_code'] ?? '',
                    'asm_id' => $row['asm_code'] ?? '',
                    'so_id' => $row['so_user_code'] ?? '',
                    'mobile_number' => $row['mobile_number'] ?? '',
                ];
            case 'DSR':
                return [
                    'name' => $row['dsr_name'] ?? '',
                    'rsm_id' => $row['rsm_code'] ?? '',
                    'asm_id' => $row['asm_code'] ?? '',
                    'so_id' => $row['so_user_code'] ?? '',
                    'distributor_id' => $row['distributor_code'] ?? '',
                    'mobile_number' => $row['dsr_contact_no'] ?? '',
                ];
            case 'RETAILER':
                return [
                    'name' => $row['retailer_name'] ?? '',
                    'rsm_id' => $row['rsm_code'] ?? '',
                    'asm_id' => $row['asm_code'] ?? '',
                    'so_id' => $row['so_user_code'] ?? '',
                    'distributor_id' => $row['distributor_code'] ?? '',
                    'dsr_id' => $row['dsr_code'] ?? '',
                    'mobile_number' => $row['mobile_number'] ?? '',
                ];
            default:
                return [];
        }
    }

    /**
     * Rocket-fast bulk insert with upsert capability
     */
    private function bulkInsertUsers(array $users): void
    {
        try {
            // Use upsert for better performance with duplicates
            User::upsert(
                $users,
                ['user_id', 'user_type'], // Unique columns
                array_keys($users[0]) // Columns to update
            );

            Log::info("HighPerformanceUserImport: Bulk inserted " . count($users) . " users");
        } catch (\Exception $e) {
            Log::error("HighPerformanceUserImport: Bulk insert failed", [
                'error' => $e->getMessage(),
                'count' => count($users)
            ]);
            throw $e;
        }
    }

    /**
     * Handle invalid rows efficiently
     */
    private function handleInvalidRows(array $invalidRows): void
    {
        // Batch process invalid rows for error logging
        foreach ($invalidRows as $invalidRow) {
            Log::warning("HighPerformanceUserImport: Invalid row", [
                'row_number' => $invalidRow['index'],
                'data' => $invalidRow['row']
            ]);
        }
    }

    /**
     * Log performance metrics
     */
    private function logPerformanceMetrics(): void
    {
        $endTime = microtime(true);
        $executionTime = $endTime - $this->startTime;
        $rowsPerSecond = $this->totalRows / $executionTime;

        Log::info("HighPerformanceUserImport: Performance Metrics", [
            'total_rows' => $this->totalRows,
            'processed_rows' => $this->processedRows,
            'execution_time' => round($executionTime, 2) . 's',
            'rows_per_second' => round($rowsPerSecond, 2),
            'memory_peak' => memory_get_peak_usage(true) / 1024 / 1024 . 'MB'
        ]);
    }

    /**
     * Optimized chunk size for rocket-fast processing
     */
    public function chunkSize(): int
    {
        return config('excel.import_optimizations.chunk_size', 2000);
    }

    /**
     * Optimized batch size for bulk operations
     */
    public function batchSize(): int
    {
        return config('excel.import_optimizations.batch_size', 1000);
    }

    /**
     * Unique columns for upsert operations
     */
    public function uniqueBy()
    {
        return ['user_id', 'user_type'];
    }

    /**
     * Cleanup method
     */
    public function __destruct()
    {
        // Re-enable foreign key checks
        if (config('excel.import_optimizations.disable_foreign_keys', true)) {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
        
        // Force garbage collection
        if (config('excel.performance.gc_collect_cycles', true)) {
            gc_collect_cycles();
        }
    }
}
