<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ImportPerformanceService
{
    /**
     * Track import performance metrics for rocket-fast optimization
     */
    public static function trackImportStart(
        string $batchId,
        string $importType,
        string $userRole,
        int $fileSizeBytes,
        int $estimatedRows = 0
    ): void {
        try {
            DB::table('import_performance_metrics')->insert([
                'batch_id' => $batchId,
                'import_type' => $importType,
                'user_role' => $userRole,
                'total_rows' => $estimatedRows,
                'processed_rows' => 0,
                'successful_rows' => 0,
                'failed_rows' => 0,
                'execution_time' => 0,
                'rows_per_second' => 0,
                'memory_peak_mb' => 0,
                'file_size_bytes' => $fileSizeBytes,
                'performance_data' => json_encode([
                    'php_memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time'),
                    'mysql_version' => DB::select('SELECT VERSION() as version')[0]->version ?? 'unknown'
                ]),
                'started_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            Log::info("ImportPerformanceService: Tracking started", [
                'batch_id' => $batchId,
                'import_type' => $importType,
                'user_role' => $userRole,
                'file_size' => $fileSizeBytes
            ]);
        } catch (\Exception $e) {
            Log::error("ImportPerformanceService: Failed to track import start", [
                'error' => $e->getMessage(),
                'batch_id' => $batchId
            ]);
        }
    }

    /**
     * Update import progress for real-time monitoring
     */
    public static function updateProgress(
        string $batchId,
        int $processedRows,
        int $successfulRows,
        int $failedRows,
        float $executionTime,
        int $memoryPeakMb
    ): void {
        try {
            $rowsPerSecond = $executionTime > 0 ? $processedRows / $executionTime : 0;

            DB::table('import_performance_metrics')
                ->where('batch_id', $batchId)
                ->update([
                    'processed_rows' => $processedRows,
                    'successful_rows' => $successfulRows,
                    'failed_rows' => $failedRows,
                    'execution_time' => $executionTime,
                    'rows_per_second' => $rowsPerSecond,
                    'memory_peak_mb' => $memoryPeakMb,
                    'updated_at' => now(),
                ]);

            // Cache current progress for real-time updates
            Cache::put("import_progress_{$batchId}", [
                'processed_rows' => $processedRows,
                'successful_rows' => $successfulRows,
                'failed_rows' => $failedRows,
                'rows_per_second' => round($rowsPerSecond, 2),
                'memory_usage' => $memoryPeakMb,
                'last_update' => now()->toISOString()
            ], 3600); // Cache for 1 hour

        } catch (\Exception $e) {
            Log::error("ImportPerformanceService: Failed to update progress", [
                'error' => $e->getMessage(),
                'batch_id' => $batchId
            ]);
        }
    }

    /**
     * Complete import tracking with final metrics
     */
    public static function trackImportComplete(
        string $batchId,
        int $totalRows,
        int $processedRows,
        int $successfulRows,
        int $failedRows,
        float $executionTime,
        int $memoryPeakMb,
        array $additionalData = []
    ): void {
        try {
            $rowsPerSecond = $executionTime > 0 ? $processedRows / $executionTime : 0;

            DB::table('import_performance_metrics')
                ->where('batch_id', $batchId)
                ->update([
                    'total_rows' => $totalRows,
                    'processed_rows' => $processedRows,
                    'successful_rows' => $successfulRows,
                    'failed_rows' => $failedRows,
                    'execution_time' => $executionTime,
                    'rows_per_second' => $rowsPerSecond,
                    'memory_peak_mb' => $memoryPeakMb,
                    'performance_data' => json_encode(array_merge([
                        'completion_rate' => $totalRows > 0 ? ($successfulRows / $totalRows) * 100 : 0,
                        'error_rate' => $totalRows > 0 ? ($failedRows / $totalRows) * 100 : 0,
                        'efficiency_score' => self::calculateEfficiencyScore($rowsPerSecond, $memoryPeakMb),
                    ], $additionalData)),
                    'completed_at' => now(),
                    'updated_at' => now(),
                ]);

            // Clear progress cache
            Cache::forget("import_progress_{$batchId}");

            Log::info("ImportPerformanceService: Import completed", [
                'batch_id' => $batchId,
                'total_rows' => $totalRows,
                'successful_rows' => $successfulRows,
                'failed_rows' => $failedRows,
                'execution_time' => $executionTime,
                'rows_per_second' => round($rowsPerSecond, 2),
                'memory_peak_mb' => $memoryPeakMb
            ]);

        } catch (\Exception $e) {
            Log::error("ImportPerformanceService: Failed to track import completion", [
                'error' => $e->getMessage(),
                'batch_id' => $batchId
            ]);
        }
    }

    /**
     * Get real-time import progress
     */
    public static function getImportProgress(string $batchId): ?array
    {
        // Try cache first for real-time data
        $cachedProgress = Cache::get("import_progress_{$batchId}");
        if ($cachedProgress) {
            return $cachedProgress;
        }

        // Fallback to database
        try {
            $metrics = DB::table('import_performance_metrics')
                ->where('batch_id', $batchId)
                ->first();

            if (!$metrics) {
                return null;
            }

            return [
                'processed_rows' => $metrics->processed_rows,
                'successful_rows' => $metrics->successful_rows,
                'failed_rows' => $metrics->failed_rows,
                'rows_per_second' => $metrics->rows_per_second,
                'memory_usage' => $metrics->memory_peak_mb,
                'execution_time' => $metrics->execution_time,
                'last_update' => $metrics->updated_at
            ];

        } catch (\Exception $e) {
            Log::error("ImportPerformanceService: Failed to get progress", [
                'error' => $e->getMessage(),
                'batch_id' => $batchId
            ]);
            return null;
        }
    }

    /**
     * Get performance analytics for optimization
     */
    public static function getPerformanceAnalytics(int $days = 30): array
    {
        try {
            $analytics = DB::table('import_performance_metrics')
                ->where('created_at', '>=', now()->subDays($days))
                ->selectRaw('
                    import_type,
                    user_role,
                    COUNT(*) as total_imports,
                    AVG(rows_per_second) as avg_rows_per_second,
                    MAX(rows_per_second) as max_rows_per_second,
                    AVG(memory_peak_mb) as avg_memory_usage,
                    AVG(execution_time) as avg_execution_time,
                    SUM(successful_rows) as total_successful_rows,
                    SUM(failed_rows) as total_failed_rows,
                    AVG(CASE WHEN total_rows > 0 THEN (successful_rows / total_rows) * 100 ELSE 0 END) as avg_success_rate
                ')
                ->groupBy('import_type', 'user_role')
                ->get();

            return [
                'period_days' => $days,
                'analytics' => $analytics->toArray(),
                'recommendations' => self::generateOptimizationRecommendations($analytics)
            ];

        } catch (\Exception $e) {
            Log::error("ImportPerformanceService: Failed to get analytics", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Calculate efficiency score (0-100)
     */
    private static function calculateEfficiencyScore(float $rowsPerSecond, int $memoryMb): float
    {
        // Base score on rows per second (higher is better)
        $speedScore = min(($rowsPerSecond / 1000) * 50, 50); // Max 50 points for speed
        
        // Memory efficiency score (lower memory usage is better)
        $memoryScore = max(50 - ($memoryMb / 100), 0); // Max 50 points for memory efficiency
        
        return round($speedScore + $memoryScore, 2);
    }

    /**
     * Generate optimization recommendations
     */
    private static function generateOptimizationRecommendations($analytics): array
    {
        $recommendations = [];

        foreach ($analytics as $metric) {
            $type = $metric->import_type;
            $role = $metric->user_role;
            
            if ($metric->avg_rows_per_second < 100) {
                $recommendations[] = [
                    'type' => 'performance',
                    'import_type' => $type,
                    'user_role' => $role,
                    'message' => "Consider using StreamingUserImport for {$role} imports to improve speed",
                    'current_speed' => round($metric->avg_rows_per_second, 2),
                    'priority' => 'high'
                ];
            }

            if ($metric->avg_memory_usage > 1024) { // > 1GB
                $recommendations[] = [
                    'type' => 'memory',
                    'import_type' => $type,
                    'user_role' => $role,
                    'message' => "High memory usage detected for {$role} imports. Consider optimizing chunk size",
                    'current_memory' => $metric->avg_memory_usage,
                    'priority' => 'medium'
                ];
            }

            if ($metric->avg_success_rate < 95) {
                $recommendations[] = [
                    'type' => 'reliability',
                    'import_type' => $type,
                    'user_role' => $role,
                    'message' => "Low success rate for {$role} imports. Review validation logic",
                    'current_rate' => round($metric->avg_success_rate, 2),
                    'priority' => 'high'
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Get top performing import configurations
     */
    public static function getTopPerformers(int $limit = 10): array
    {
        try {
            return DB::table('import_performance_metrics')
                ->where('completed_at', '>=', now()->subDays(30))
                ->where('total_rows', '>', 1000) // Only consider substantial imports
                ->orderBy('rows_per_second', 'desc')
                ->limit($limit)
                ->get([
                    'batch_id',
                    'import_type',
                    'user_role',
                    'total_rows',
                    'rows_per_second',
                    'memory_peak_mb',
                    'execution_time',
                    'completed_at'
                ])
                ->toArray();

        } catch (\Exception $e) {
            Log::error("ImportPerformanceService: Failed to get top performers", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Optimize import settings based on file size and type
     */
    public static function getOptimalSettings(int $fileSizeBytes, string $userRole): array
    {
        // Determine optimal import type based on file size
        $importType = 'high_performance';
        $chunkSize = 2000;
        $batchSize = 1000;

        if ($fileSizeBytes > 100 * 1024 * 1024) { // > 100MB
            $importType = 'streaming';
            $chunkSize = 5000;
            $batchSize = 2000;
        } elseif ($fileSizeBytes > 50 * 1024 * 1024) { // > 50MB
            $importType = 'streaming';
            $chunkSize = 3000;
            $batchSize = 1500;
        }

        // Role-specific optimizations
        $roleOptimizations = [
            'RETAILER' => ['chunk_size' => $chunkSize * 0.8], // Retailers have more complex data
            'HIERARCHY' => ['chunk_size' => $chunkSize * 1.2], // Hierarchy is simpler
        ];

        $settings = [
            'import_type' => $importType,
            'chunk_size' => $chunkSize,
            'batch_size' => $batchSize,
            'memory_limit' => $fileSizeBytes > 100 * 1024 * 1024 ? '4096M' : '2048M',
            'timeout' => $fileSizeBytes > 100 * 1024 * 1024 ? 7200 : 3600, // 2 hours for large files
        ];

        // Apply role-specific optimizations
        if (isset($roleOptimizations[$userRole])) {
            $settings = array_merge($settings, $roleOptimizations[$userRole]);
        }

        return $settings;
    }
}
