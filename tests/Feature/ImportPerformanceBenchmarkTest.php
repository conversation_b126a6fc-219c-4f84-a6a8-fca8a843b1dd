<?php

namespace Tests\Feature;

use App\Imports\HighPerformanceUserImport;
use App\Imports\StreamingUserImport;
use App\Imports\UserMasterImport;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

class ImportPerformanceBenchmarkTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->actingAs(User::factory()->create([
            'user_type' => 'ADMIN',
            'user_id' => 'admin_test'
        ]));
    }

    /**
     * Benchmark: Compare original vs high-performance import
     */
    public function test_benchmark_original_vs_high_performance(): void
    {
        $this->markTestSkipped('Benchmark test - run manually for performance comparison');
        
        $testSizes = [100, 500, 1000, 2000];
        $results = [];
        
        foreach ($testSizes as $size) {
            $this->info("Benchmarking with {$size} rows...");
            
            // Test original import
            $originalTime = $this->benchmarkOriginalImport($size);
            
            // Reset database
            $this->refreshDatabase();
            $this->setUp();
            
            // Test high-performance import
            $highPerfTime = $this->benchmarkHighPerformanceImport($size);
            
            // Reset database
            $this->refreshDatabase();
            $this->setUp();
            
            $improvement = (($originalTime - $highPerfTime) / $originalTime) * 100;
            
            $results[$size] = [
                'original_time' => $originalTime,
                'high_perf_time' => $highPerfTime,
                'improvement_percent' => $improvement,
                'speedup_factor' => $originalTime / $highPerfTime
            ];
            
            $this->info("Results for {$size} rows:");
            $this->info("  Original: " . number_format($originalTime, 3) . "s");
            $this->info("  High-Performance: " . number_format($highPerfTime, 3) . "s");
            $this->info("  Improvement: " . number_format($improvement, 1) . "%");
            $this->info("  Speedup: " . number_format($originalTime / $highPerfTime, 2) . "x");
            $this->info("");
        }
        
        // Assert overall improvement
        foreach ($results as $size => $result) {
            $this->assertGreaterThan(0, $result['improvement_percent'], 
                "High-performance import should be faster for {$size} rows");
        }
        
        // Save benchmark results
        $this->saveBenchmarkResults($results);
    }

    /**
     * Benchmark: Memory usage comparison
     */
    public function test_benchmark_memory_usage(): void
    {
        $this->markTestSkipped('Memory benchmark test - run manually');
        
        $testSizes = [1000, 5000, 10000];
        $results = [];
        
        foreach ($testSizes as $size) {
            $this->info("Memory benchmark with {$size} rows...");
            
            // Test original import memory
            $originalMemory = $this->benchmarkMemoryUsage('original', $size);
            
            // Reset
            gc_collect_cycles();
            
            // Test high-performance import memory
            $highPerfMemory = $this->benchmarkMemoryUsage('high_performance', $size);
            
            // Reset
            gc_collect_cycles();
            
            // Test streaming import memory
            $streamingMemory = $this->benchmarkMemoryUsage('streaming', $size);
            
            $results[$size] = [
                'original_memory' => $originalMemory,
                'high_perf_memory' => $highPerfMemory,
                'streaming_memory' => $streamingMemory
            ];
            
            $this->info("Memory usage for {$size} rows:");
            $this->info("  Original: " . number_format($originalMemory, 1) . "MB");
            $this->info("  High-Performance: " . number_format($highPerfMemory, 1) . "MB");
            $this->info("  Streaming: " . number_format($streamingMemory, 1) . "MB");
            $this->info("");
        }
        
        // Assert memory efficiency
        foreach ($results as $size => $result) {
            $this->assertLessThanOrEqual($result['original_memory'], $result['high_perf_memory'],
                "High-performance import should use same or less memory for {$size} rows");
            $this->assertLessThanOrEqual($result['high_perf_memory'], $result['streaming_memory'],
                "Streaming import should be most memory efficient for {$size} rows");
        }
    }

    /**
     * Benchmark: Throughput test
     */
    public function test_benchmark_throughput(): void
    {
        $this->markTestSkipped('Throughput benchmark test - run manually');
        
        $testSizes = [1000, 5000, 10000, 20000];
        $results = [];
        
        foreach ($testSizes as $size) {
            $this->info("Throughput test with {$size} rows...");
            
            $file = $this->createBenchmarkFile($size);
            
            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);
            
            Excel::import(new HighPerformanceUserImport('RSM', 'benchmark_' . $size), $file->getRealPath());
            
            $endTime = microtime(true);
            $peakMemory = memory_get_peak_usage(true);
            
            $executionTime = $endTime - $startTime;
            $memoryUsed = ($peakMemory - $startMemory) / 1024 / 1024; // MB
            $throughput = $size / $executionTime; // rows per second
            
            $results[$size] = [
                'execution_time' => $executionTime,
                'memory_used' => $memoryUsed,
                'throughput' => $throughput,
                'memory_per_row' => $memoryUsed / $size * 1024 // KB per row
            ];
            
            $this->info("Results for {$size} rows:");
            $this->info("  Time: " . number_format($executionTime, 3) . "s");
            $this->info("  Memory: " . number_format($memoryUsed, 1) . "MB");
            $this->info("  Throughput: " . number_format($throughput, 0) . " rows/s");
            $this->info("  Memory per row: " . number_format($memoryUsed / $size * 1024, 2) . "KB");
            $this->info("");
            
            // Reset for next test
            $this->refreshDatabase();
            $this->setUp();
        }
        
        // Assert performance targets
        foreach ($results as $size => $result) {
            $this->assertGreaterThan(100, $result['throughput'], 
                "Should process at least 100 rows/second for {$size} rows");
            $this->assertLessThan(1, $result['memory_per_row'], 
                "Should use less than 1KB memory per row for {$size} rows");
        }
        
        $this->saveThroughputResults($results);
    }

    /**
     * Benchmark original import
     */
    private function benchmarkOriginalImport(int $size): float
    {
        $file = $this->createBenchmarkFile($size);
        
        $startTime = microtime(true);
        Excel::import(new UserMasterImport('RSM', 'original_' . $size), $file->getRealPath());
        $endTime = microtime(true);
        
        return $endTime - $startTime;
    }

    /**
     * Benchmark high-performance import
     */
    private function benchmarkHighPerformanceImport(int $size): float
    {
        $file = $this->createBenchmarkFile($size);
        
        $startTime = microtime(true);
        Excel::import(new HighPerformanceUserImport('RSM', 'highperf_' . $size), $file->getRealPath());
        $endTime = microtime(true);
        
        return $endTime - $startTime;
    }

    /**
     * Benchmark memory usage
     */
    private function benchmarkMemoryUsage(string $type, int $size): float
    {
        $file = $this->createBenchmarkFile($size);
        
        $startMemory = memory_get_usage(true);
        
        switch ($type) {
            case 'original':
                Excel::import(new UserMasterImport('RSM', 'mem_original_' . $size), $file->getRealPath());
                break;
            case 'high_performance':
                Excel::import(new HighPerformanceUserImport('RSM', 'mem_highperf_' . $size), $file->getRealPath());
                break;
            case 'streaming':
                Excel::import(new StreamingUserImport('RSM', 'mem_streaming_' . $size), $file->getRealPath());
                break;
        }
        
        $peakMemory = memory_get_peak_usage(true);
        
        return ($peakMemory - $startMemory) / 1024 / 1024; // MB
    }

    /**
     * Create benchmark file
     */
    private function createBenchmarkFile(int $rows): UploadedFile
    {
        $data = [];
        $data[] = ['rsmrbdm_code', 'rsmrbdm_name', 'email_id', 'mobile_number', 'region_name', 'city', 'state', 'pincode', 'cfa_code'];
        
        for ($i = 1; $i <= $rows; $i++) {
            $data[] = [
                "BENCH_RSM_{$i}",
                "Benchmark RSM {$i}",
                "bench_rsm{$i}@test.com",
                "9876543" . str_pad($i % 1000, 3, '0', STR_PAD_LEFT),
                "Benchmark Region {$i}",
                "Benchmark City {$i}",
                "Benchmark State",
                "12345{$i}",
                "BENCH_CFA_001"
            ];
        }
        
        $filename = "benchmark_{$rows}.xlsx";
        $path = storage_path("app/testing/{$filename}");
        
        // Create directory if it doesn't exist
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        // Create Excel file
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        foreach ($data as $rowIndex => $rowData) {
            foreach ($rowData as $colIndex => $cellData) {
                $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
            }
        }
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($path);
        
        return new UploadedFile($path, $filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', null, true);
    }

    /**
     * Save benchmark results
     */
    private function saveBenchmarkResults(array $results): void
    {
        $reportPath = storage_path('app/testing/benchmark_results.json');
        
        $report = [
            'timestamp' => now()->toISOString(),
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'results' => $results
        ];
        
        file_put_contents($reportPath, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->info("Benchmark results saved to: {$reportPath}");
    }

    /**
     * Save throughput results
     */
    private function saveThroughputResults(array $results): void
    {
        $reportPath = storage_path('app/testing/throughput_results.json');
        
        $report = [
            'timestamp' => now()->toISOString(),
            'test_type' => 'throughput_benchmark',
            'results' => $results
        ];
        
        file_put_contents($reportPath, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->info("Throughput results saved to: {$reportPath}");
    }

    /**
     * Helper method to output info during tests
     */
    private function info(string $message): void
    {
        if (app()->runningInConsole()) {
            echo $message . "\n";
        }
    }
}
