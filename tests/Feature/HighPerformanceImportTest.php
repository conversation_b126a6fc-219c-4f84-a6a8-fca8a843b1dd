<?php

namespace Tests\Feature;

use App\Imports\HighPerformanceUserImport;
use App\Imports\StreamingUserImport;
use App\Jobs\HighPerformanceImportJob;
use App\Models\User;
use App\Services\ImportPerformanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

class HighPerformanceImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->actingAs(User::factory()->create([
            'user_type' => 'ADMIN',
            'user_id' => 'admin_test'
        ]));
    }

    /**
     * Test high-performance import with small file
     */
    public function test_high_performance_import_small_file(): void
    {
        // Create test Excel file
        $file = $this->createTestExcelFile('small', 100);
        
        $startTime = microtime(true);
        
        // Test the import
        Excel::import(new HighPerformanceUserImport('RSM', 'test_batch_001'), $file->getRealPath());
        
        $executionTime = microtime(true) - $startTime;
        
        // Assertions
        $this->assertDatabaseCount('users', 101); // 100 + 1 admin user
        $this->assertLessThan(5, $executionTime, 'Import should complete in under 5 seconds');
        
        // Check data quality
        $importedUser = User::where('user_type', 'RSM')->first();
        $this->assertNotNull($importedUser);
        $this->assertNotEmpty($importedUser->name);
    }

    /**
     * Test streaming import with large file
     */
    public function test_streaming_import_large_file(): void
    {
        // Create test Excel file with more rows
        $file = $this->createTestExcelFile('large', 5000);
        
        $startTime = microtime(true);
        
        // Test the streaming import
        Excel::import(new StreamingUserImport('ASM', 'test_batch_002'), $file->getRealPath());
        
        $executionTime = microtime(true) - $startTime;
        
        // Assertions
        $this->assertDatabaseCount('users', 5001); // 5000 + 1 admin user
        $this->assertLessThan(30, $executionTime, 'Streaming import should complete in under 30 seconds');
        
        // Check performance metrics
        $rowsPerSecond = 5000 / $executionTime;
        $this->assertGreaterThan(100, $rowsPerSecond, 'Should process at least 100 rows per second');
    }

    /**
     * Test import job queuing
     */
    public function test_import_job_queuing(): void
    {
        Queue::fake();
        
        $file = UploadedFile::fake()->create('test.xlsx', 1024); // 1MB file
        
        // Dispatch job
        HighPerformanceImportJob::dispatch(
            'test/path.xlsx',
            'DISTRIBUTOR',
            'test_batch_003',
            'high_performance',
            1024 * 1024,
            'test_user',
            'Test User'
        );
        
        // Assert job was queued
        Queue::assertPushed(HighPerformanceImportJob::class);
    }

    /**
     * Test performance tracking
     */
    public function test_performance_tracking(): void
    {
        $batchId = 'test_batch_004';
        
        // Start tracking
        ImportPerformanceService::trackImportStart(
            $batchId,
            'high_performance',
            'SO',
            1024 * 1024,
            1000
        );
        
        // Update progress
        ImportPerformanceService::updateProgress(
            $batchId,
            500,
            480,
            20,
            2.5,
            128
        );
        
        // Complete tracking
        ImportPerformanceService::trackImportComplete(
            $batchId,
            1000,
            1000,
            980,
            20,
            5.0,
            256
        );
        
        // Assert tracking data
        $this->assertDatabaseHas('import_performance_metrics', [
            'batch_id' => $batchId,
            'import_type' => 'high_performance',
            'user_role' => 'SO',
            'total_rows' => 1000,
            'successful_rows' => 980,
            'failed_rows' => 20
        ]);
        
        // Test progress retrieval
        $progress = ImportPerformanceService::getImportProgress($batchId);
        $this->assertNotNull($progress);
        $this->assertEquals(980, $progress['successful_rows']);
    }

    /**
     * Test memory efficiency
     */
    public function test_memory_efficiency(): void
    {
        $initialMemory = memory_get_usage(true);
        
        // Create and process a moderately large file
        $file = $this->createTestExcelFile('medium', 2000);
        
        Excel::import(new HighPerformanceUserImport('DSR', 'test_batch_005'), $file->getRealPath());
        
        $peakMemory = memory_get_peak_usage(true);
        $memoryIncrease = ($peakMemory - $initialMemory) / 1024 / 1024; // MB
        
        // Assert memory usage is reasonable (less than 500MB for 2000 rows)
        $this->assertLessThan(500, $memoryIncrease, 'Memory usage should be under 500MB');
        
        // Force garbage collection and check memory cleanup
        gc_collect_cycles();
        $finalMemory = memory_get_usage(true);
        $memoryRetained = ($finalMemory - $initialMemory) / 1024 / 1024; // MB
        
        $this->assertLessThan(100, $memoryRetained, 'Memory should be cleaned up after import');
    }

    /**
     * Test bulk insert performance
     */
    public function test_bulk_insert_performance(): void
    {
        $users = [];
        $batchSize = 1000;
        
        // Prepare bulk data
        for ($i = 1; $i <= $batchSize; $i++) {
            $users[] = [
                'user_id' => "bulk_test_{$i}",
                'user_type' => 'RETAILER',
                'name' => "Test User {$i}",
                'status' => 1,
                'password' => bcrypt("bulk_test_{$i}"),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        
        $startTime = microtime(true);
        
        // Perform bulk insert
        User::upsert(
            $users,
            ['user_id', 'user_type'],
            array_keys($users[0])
        );
        
        $executionTime = microtime(true) - $startTime;
        
        // Assertions
        $this->assertDatabaseCount('users', $batchSize + 1); // +1 for admin user
        $this->assertLessThan(2, $executionTime, 'Bulk insert should complete in under 2 seconds');
        
        $rowsPerSecond = $batchSize / $executionTime;
        $this->assertGreaterThan(500, $rowsPerSecond, 'Should insert at least 500 rows per second');
    }

    /**
     * Test error handling and recovery
     */
    public function test_error_handling(): void
    {
        // Create file with some invalid data
        $file = $this->createTestExcelFileWithErrors();
        
        $import = new HighPerformanceUserImport('VE', 'test_batch_006');
        
        // Import should not throw exception but handle errors gracefully
        Excel::import($import, $file->getRealPath());
        
        // Check that valid rows were imported and invalid ones were skipped
        $validUsers = User::where('user_type', 'VE')->count();
        $this->assertGreaterThan(0, $validUsers, 'Valid rows should be imported');
        $this->assertLessThan(100, $validUsers, 'Invalid rows should be skipped');
    }

    /**
     * Test concurrent imports
     */
    public function test_concurrent_imports(): void
    {
        Queue::fake();
        
        $files = [
            ['path' => 'test1.xlsx', 'role' => 'RSM', 'batch' => 'concurrent_001'],
            ['path' => 'test2.xlsx', 'role' => 'ASM', 'batch' => 'concurrent_002'],
            ['path' => 'test3.xlsx', 'role' => 'SO', 'batch' => 'concurrent_003'],
        ];
        
        // Dispatch multiple jobs
        foreach ($files as $file) {
            HighPerformanceImportJob::dispatch(
                $file['path'],
                $file['role'],
                $file['batch'],
                'high_performance',
                1024 * 1024
            );
        }
        
        // Assert all jobs were queued
        Queue::assertPushed(HighPerformanceImportJob::class, 3);
    }

    /**
     * Test performance analytics
     */
    public function test_performance_analytics(): void
    {
        // Create some test performance data
        $batches = [
            ['batch_id' => 'analytics_001', 'type' => 'high_performance', 'role' => 'RSM'],
            ['batch_id' => 'analytics_002', 'type' => 'streaming', 'role' => 'ASM'],
            ['batch_id' => 'analytics_003', 'type' => 'high_performance', 'role' => 'SO'],
        ];
        
        foreach ($batches as $batch) {
            ImportPerformanceService::trackImportStart(
                $batch['batch_id'],
                $batch['type'],
                $batch['role'],
                1024 * 1024,
                1000
            );
            
            ImportPerformanceService::trackImportComplete(
                $batch['batch_id'],
                1000,
                1000,
                950,
                50,
                5.0,
                256
            );
        }
        
        // Get analytics
        $analytics = ImportPerformanceService::getPerformanceAnalytics(30);
        
        $this->assertNotEmpty($analytics['analytics']);
        $this->assertArrayHasKey('recommendations', $analytics);
    }

    /**
     * Create test Excel file
     */
    private function createTestExcelFile(string $size, int $rows): UploadedFile
    {
        $data = [];
        $data[] = ['rsmrbdm_code', 'rsmrbdm_name', 'email_id', 'mobile_number', 'region_name', 'city', 'state', 'pincode', 'cfa_code'];
        
        for ($i = 1; $i <= $rows; $i++) {
            $data[] = [
                "RSM_{$i}",
                "Test RSM {$i}",
                "rsm{$i}@test.com",
                "9876543210",
                "Test Region",
                "Test City",
                "Test State",
                "123456",
                "CFA_001"
            ];
        }
        
        $filename = "test_{$size}_{$rows}.xlsx";
        $path = storage_path("app/testing/{$filename}");
        
        // Create directory if it doesn't exist
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        // Create Excel file using PhpSpreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        foreach ($data as $rowIndex => $rowData) {
            foreach ($rowData as $colIndex => $cellData) {
                $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
            }
        }
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($path);
        
        return new UploadedFile($path, $filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', null, true);
    }

    /**
     * Create test Excel file with errors
     */
    private function createTestExcelFileWithErrors(): UploadedFile
    {
        $data = [];
        $data[] = ['rsmrbdm_code', 'rsmrbdm_name', 'email_id', 'mobile_number'];
        
        // Add valid rows
        for ($i = 1; $i <= 50; $i++) {
            $data[] = ["VE_{$i}", "Test VE {$i}", "ve{$i}@test.com", "9876543210"];
        }
        
        // Add invalid rows (missing required fields)
        for ($i = 51; $i <= 100; $i++) {
            $data[] = ["", "Test VE {$i}", "invalid_email", ""]; // Missing user_id and invalid email
        }
        
        $filename = "test_with_errors.xlsx";
        $path = storage_path("app/testing/{$filename}");
        
        // Create directory if it doesn't exist
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        // Create Excel file
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        foreach ($data as $rowIndex => $rowData) {
            foreach ($rowData as $colIndex => $cellData) {
                $sheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex + 1, $cellData);
            }
        }
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($path);
        
        return new UploadedFile($path, $filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', null, true);
    }
}
