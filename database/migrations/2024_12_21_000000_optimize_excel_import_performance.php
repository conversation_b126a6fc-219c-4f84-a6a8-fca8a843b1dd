<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations for rocket-fast Excel import performance
     */
    public function up(): void
    {
        // Optimize users table for high-performance imports
        $this->optimizeUsersTable();
        
        // Add performance indexes
        $this->addPerformanceIndexes();
        
        // Optimize database configuration
        $this->optimizeDatabaseSettings();
        
        // Create import performance tracking table
        $this->createImportPerformanceTable();
    }

    /**
     * Optimize users table structure for faster imports
     */
    private function optimizeUsersTable(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add composite indexes for faster lookups during import
            if (!$this->indexExists('users', 'idx_user_type_status')) {
                $table->index(['user_type', 'status'], 'idx_user_type_status');
            }
            
            if (!$this->indexExists('users', 'idx_user_id_type')) {
                $table->index(['user_id', 'user_type'], 'idx_user_id_type');
            }
            
            if (!$this->indexExists('users', 'idx_hierarchy_lookup')) {
                $table->index(['rsm_id', 'asm_id', 'so_id'], 'idx_hierarchy_lookup');
            }
            
            if (!$this->indexExists('users', 'idx_mobile_email')) {
                $table->index(['mobile_number', 'email'], 'idx_mobile_email');
            }
            
            if (!$this->indexExists('users', 'idx_location_lookup')) {
                $table->index(['state', 'city', 'pin_code'], 'idx_location_lookup');
            }
            
            if (!$this->indexExists('users', 'idx_cfa_code')) {
                $table->index('cfa_code', 'idx_cfa_code');
            }
        });
        
        echo "✅ Users table optimized for rocket-fast imports\n";
    }

    /**
     * Add performance indexes for related tables
     */
    private function addPerformanceIndexes(): void
    {
        // Optimize upload_histories table
        if (Schema::hasTable('upload_histories')) {
            Schema::table('upload_histories', function (Blueprint $table) {
                if (!$this->indexExists('upload_histories', 'idx_batch_upload_type')) {
                    $table->index(['batchID', 'uploadType'], 'idx_batch_upload_type');
                }
                
                if (!$this->indexExists('upload_histories', 'idx_upload_datetime')) {
                    $table->index('uploadDateTime', 'idx_upload_datetime');
                }
                
                if (!$this->indexExists('upload_histories', 'idx_upload_by_type')) {
                    $table->index(['uploadBy', 'userType'], 'idx_upload_by_type');
                }
            });
        }

        // Optimize asset_error_logs table
        if (Schema::hasTable('asset_error_logs')) {
            Schema::table('asset_error_logs', function (Blueprint $table) {
                if (!$this->indexExists('asset_error_logs', 'idx_batch_error_type')) {
                    $table->index(['batch_id', 'failed_type'], 'idx_batch_error_type');
                }
                
                if (!$this->indexExists('asset_error_logs', 'idx_uploaded_time')) {
                    $table->index('uploadedTime', 'idx_uploaded_time');
                }
            });
        }

        // Optimize jobs table for queue performance
        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                if (!$this->indexExists('jobs', 'idx_queue_available_at')) {
                    $table->index(['queue', 'available_at'], 'idx_queue_available_at');
                }
                
                if (!$this->indexExists('jobs', 'idx_reserved_at')) {
                    $table->index('reserved_at', 'idx_reserved_at');
                }
            });
        }

        echo "✅ Performance indexes added for rocket-fast processing\n";
    }

    /**
     * Optimize database settings for high-performance imports
     */
    private function optimizeDatabaseSettings(): void
    {
        try {
            // Optimize MySQL settings for bulk operations
            $optimizations = [
                'SET SESSION innodb_buffer_pool_size = 2147483648', // 2GB
                'SET SESSION innodb_log_file_size = 268435456',      // 256MB
                'SET SESSION innodb_log_buffer_size = 67108864',     // 64MB
                'SET SESSION innodb_flush_log_at_trx_commit = 2',    // Better performance
                'SET SESSION innodb_flush_method = O_DIRECT',        // Direct I/O
                'SET SESSION innodb_doublewrite = 0',                // Disable for speed
                'SET SESSION sync_binlog = 0',                       // Disable for imports
                'SET SESSION sql_log_bin = 0',                       // Disable binary logging
                'SET SESSION autocommit = 1',                       // Enable autocommit
                'SET SESSION unique_checks = 1',                    // Enable unique checks
                'SET SESSION foreign_key_checks = 1',               // Enable FK checks
            ];

            foreach ($optimizations as $sql) {
                try {
                    DB::statement($sql);
                } catch (\Exception $e) {
                    // Some settings might not be available in all MySQL versions
                    echo "⚠️  Warning: {$sql} - {$e->getMessage()}\n";
                }
            }

            echo "✅ Database settings optimized for rocket-fast imports\n";
        } catch (\Exception $e) {
            echo "⚠️  Database optimization warning: {$e->getMessage()}\n";
        }
    }

    /**
     * Create table for tracking import performance metrics
     */
    private function createImportPerformanceTable(): void
    {
        if (!Schema::hasTable('import_performance_metrics')) {
            Schema::create('import_performance_metrics', function (Blueprint $table) {
                $table->id();
                $table->string('batch_id')->index();
                $table->string('import_type'); // 'high_performance', 'streaming', 'standard'
                $table->string('user_role');
                $table->integer('total_rows');
                $table->integer('processed_rows');
                $table->integer('successful_rows');
                $table->integer('failed_rows');
                $table->decimal('execution_time', 10, 3); // seconds
                $table->decimal('rows_per_second', 10, 2);
                $table->integer('memory_peak_mb');
                $table->integer('file_size_bytes');
                $table->json('performance_data')->nullable();
                $table->timestamp('started_at');
                $table->timestamp('completed_at')->nullable();
                $table->timestamps();
                
                // Indexes for performance tracking
                $table->index(['import_type', 'user_role']);
                $table->index(['started_at', 'completed_at']);
                $table->index('rows_per_second');
            });
            
            echo "✅ Import performance tracking table created\n";
        }
    }

    /**
     * Check if index exists
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$index]);
        return !empty($indexes);
    }

    /**
     * Reverse the migrations
     */
    public function down(): void
    {
        // Drop performance indexes
        Schema::table('users', function (Blueprint $table) {
            $indexes = [
                'idx_user_type_status',
                'idx_user_id_type', 
                'idx_hierarchy_lookup',
                'idx_mobile_email',
                'idx_location_lookup',
                'idx_cfa_code'
            ];
            
            foreach ($indexes as $index) {
                if ($this->indexExists('users', $index)) {
                    $table->dropIndex($index);
                }
            }
        });

        if (Schema::hasTable('upload_histories')) {
            Schema::table('upload_histories', function (Blueprint $table) {
                $indexes = ['idx_batch_upload_type', 'idx_upload_datetime', 'idx_upload_by_type'];
                foreach ($indexes as $index) {
                    if ($this->indexExists('upload_histories', $index)) {
                        $table->dropIndex($index);
                    }
                }
            });
        }

        if (Schema::hasTable('asset_error_logs')) {
            Schema::table('asset_error_logs', function (Blueprint $table) {
                $indexes = ['idx_batch_error_type', 'idx_uploaded_time'];
                foreach ($indexes as $index) {
                    if ($this->indexExists('asset_error_logs', $index)) {
                        $table->dropIndex($index);
                    }
                }
            });
        }

        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                $indexes = ['idx_queue_available_at', 'idx_reserved_at'];
                foreach ($indexes as $index) {
                    if ($this->indexExists('jobs', $index)) {
                        $table->dropIndex($index);
                    }
                }
            });
        }

        // Drop performance tracking table
        Schema::dropIfExists('import_performance_metrics');
        
        echo "✅ Performance optimizations rolled back\n";
    }
};
